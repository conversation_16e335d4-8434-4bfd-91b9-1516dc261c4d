#include "uv_manager_pedk.h"
#include "transport/transport.h"
#include <zmq.h>
#include "basic/config.h"
#include "pedk/pedk_manager/app_manager.h"
#include "pedk/pedk_cmd/pedk_cmd_context.h"
#include "pedk/pedk_transport/transport_pedk.h"

void pedk_msg_read_cb(uv_poll_t* handle, int status, int events)
{
    Transport* trans = (Transport*)handle->data;

    //LOG_D("pedk_msg_read_cb")

    if (events & UV_READABLE) {

        //LOG_D("events & UV_READABLE");

        int event;
        size_t events_size = sizeof(event);
        void* socket = trans->get_handle();
        zmq_getsockopt(socket, ZMQ_EVENTS, &event, &events_size);

        if (event & ZMQ_POLLIN) {
            std::string msg_stream;
            uint32_t ret = 0;

           // LOG_D("event & ZMQ_POLLIN");

            //获取自己进程的cmd_context            
            CmdContext* cmd_context = PedkCmdContext::get_instance();
            while(0 == trans->recv(msg_stream)){
                ret = cmd_context->deserialize(msg_stream);
            }
        }
    }
}

uvManagerPedk::uvManagerPedk(Transport *trans):uvManager(trans)
{
    LOG_D("uvManagerPedk");

    m_uv_loop = uv_loop_new();
    m_uv_poll.data = m_trans;

    int fd = m_trans->get_fd();
    
    LOG_D("fd = %d",fd);

    uv_poll_init(m_uv_loop, &m_uv_poll, fd);
    uv_poll_start(&m_uv_poll, UV_READABLE, pedk_msg_read_cb);
}

uvManagerPedk::~uvManagerPedk()
{
}

void uvManagerPedk::run()
{
    uv_run(m_uv_loop, UV_RUN_DEFAULT);
    LOG_D("pedk uv run stop");
}

#include "cmd/cmds/app_cmds/app_system_error_cmd.h"
//pedk的心跳检查流程
static void heartbeat_check_cb(uv_timer_t* handle)
{
    //检查心跳
    //LOG_D("heartbeat_cb");
    AppManager *app_manager = AppManager::get_instance();
    std::string ret = app_manager->check_hearbeat();
    if("" != ret){
        //if 有异常{}
        LOG_E("[%s] timeout!!!",ret.c_str());
        //TODO:打一个log提示开发者。完整应该是强制弹出一个画面。
        /*AppSystemErrorCmd app_system_error_cmd("timeout",ret);
        std::string ss = app_system_error_cmd.serialize();
        
        TransportPedk *trans = TransportPedk::get_instance();
        LOG_I("MSG_PING_RES ->");
        trans->send_to_app("Broadcast",ss);*/
    }
}

void uvManagerPedk::start_heartbeat_check()
{
    m_heart_beat.data = m_trans;
    uv_timer_init(m_uv_loop, &m_heart_beat);

    // 启动定时器，每隔30秒检查一次
    uv_timer_start(&m_heart_beat, heartbeat_check_cb, HEARTBEAT_TIME, HEARTBEAT_TIME);
}
                                                                                                                                                                                  