#ifndef _RET_STATIC_CMD_H_
#define _RET_STATIC_CMD_H_

#include "cmd/cmds/pedk_cmds/pedk_cmds.h"
#include "cmd/exec/pedk_cmd_exec.h"

typedef struct RetStaticCmdFormat {
    uint32_t type; // 指令类型
    std::string app_name;
    std::string msg;

    MSGPACK_DEFINE(type,app_name,msg);
} RetStaticCmdFormat;

class RetStaticCmd : public PedkCmd {
public:
    RetStaticCmd();
    RetStaticCmd(std::string app_name,std::string msg);
    ~RetStaticCmd();
    uint32_t execute(PedkCmdExec *pedk_cmd_exec) override;
    std::string serialize() override;
    uint32_t deserialize(std::string ss) override;
protected:
private:
    RetStaticCmdFormat m_ret_static_cmd;
};

#endif // _RET_STATIC_CMD_H_