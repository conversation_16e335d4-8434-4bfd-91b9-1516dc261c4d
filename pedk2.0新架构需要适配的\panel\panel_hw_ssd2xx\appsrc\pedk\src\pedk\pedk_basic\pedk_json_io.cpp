#include "basic/json_io/json_io.h"
#include "pedk/pedk_basic/pedk_json_io.h"
#include "pedk/pedk_manager/app_manager.h"
#include <fstream>
#include <nlohmann/json.hpp>
#include <iostream>

/**
 * @brief 从app的projec.config.json文件中，获取自启动信息
 * 
 * @param json_str app中project.config.json文件中的内容
 * @return true 是自启动app
 * @return false 不是自启动app
 */
bool PedkJsonIO::get_autoboot(std::string json_str)
{
    bool ret;

    try {
        json json_data;
        json_data = json::parse(json_str);

        if (json_data["autoboot"].is_boolean()) {
            ret = json_data["autoboot"].get<bool>();
        } else {
           ret = false;
        }
    } catch (json::exception& e) {
        LOG_E("JSON error: %s",e.what());
    }

    return ret;
}
/**
 * @brief 从app的projec.config.json文件中，获取开始时间
 * 
 * @param json_str 
 * @return int64_t 
 */
int64_t PedkJsonIO::get_starttime(std::string json_str)
{
    int64_t ret = 0;

    try {
        json json_data;
        json_data = json::parse(json_str);

        if (json_data["start_time"].is_number_integer()) {
            ret = json_data["start_time"].get<int64_t>();
        } else {
           ret = 0;
        }
    } catch (json::exception& e) {
        LOG_E("JSON error: %s",e.what());
    }

    return ret;
}

/**
 * @brief 从app的projec.config.json文件中，获取结束时间
 * 
 * @param json_str 
 * @return int64_t 
 */
int64_t PedkJsonIO::get_endtime(std::string json_str)
{
    int64_t ret = 0;

    try {
        json json_data;
        json_data = json::parse(json_str);

        if (json_data["valid_time"].is_number_integer()) {
            ret = json_data["valid_time"].get<int64_t>();
        } else {
           ret = 0;
        }
    } catch (json::exception& e) {
        LOG_E("JSON error: %s",e.what());
    }

    return ret;
}


/**
 * @brief 从用户app的app.config.json文件中获取用户自定义配置
 * 
 * @param json_str 
 * @return UserAppConfig 
 */
UserAppConfig PedkJsonIO::get_user_app_config_json(std::string json_str)
{
    UserAppConfig ret;

    try {
        json json_data;
        json_data = json::parse(json_str);

        if (json_data["icon"].is_string()) {
            ret.icon_path = json_data["icon"].get<std::string>();
        } else {
            ret.icon_path = "";
        }

        if (json_data["display_name"].is_string()) {
            ret.display_name = json_data["display_name"].get<std::string>();
        } else {
            ret.display_name = "";
        }
    } catch (json::exception& e) {
        LOG_E("JSON error: %s",e.what());
    }

    return ret;
}

/**
 **************************************************************
 *                   海盗出没，小心Bug                          *
 *   .=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-.       *
 *    |                     ______                     |      *
 *    |                  .-"      "-.                  |      *
 *    |                 /            \                 |      *
 *    |     _          |              |          _     |      *
 *    |    ( \         |,  .-.  .-.  ,|         / )    |      *
 *    |     > "=._     | )(__/  \__)( |     _.=" <     |      *
 *    |    (_/"=._"=._ |/     /\     \| _.="_.="\_)    |      *
 *    |           "=._"(_     ^^     _)"_.="           |      *
 *    |               "=\__|IIIIII|__/="               |      *
 *    |              _.="| \IIIIII/ |"=._              |      *
 *    |    _     _.="_.="\          /"=._"=._     _    |      *
 *    |   ( \_.="_.="     `--------`     "=._"=._/ )   |      *
 *    |    > _.="                            "=._ <    |      *
 *    |   (_/                                    \_)   |      *
 *    |                                                |      *
 *    '-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-='      *
 *                                                            *
 *           LASCIATE OGNI SPERANZA, VOI CH'ENTRATE           *
 **************************************************************
 */
