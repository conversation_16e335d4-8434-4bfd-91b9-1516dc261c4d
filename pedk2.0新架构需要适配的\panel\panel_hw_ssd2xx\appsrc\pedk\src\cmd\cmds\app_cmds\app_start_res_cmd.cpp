#include "app_start_res_cmd.h"

AppStartResCmd::AppStartResCmd()
{
    m_app_start_res_cmd.type = MSG_APP_START_RES;
    m_app_start_res_cmd.app_name = "";
    m_app_start_res_cmd.res = false;
}

 AppStartResCmd::AppStartResCmd (std::string app_name, bool res)
 {
    m_app_start_res_cmd.type = MSG_APP_START_RES;
    m_app_start_res_cmd.app_name = app_name;
    m_app_start_res_cmd.res = res;
 }

AppStartResCmd::~AppStartResCmd()
{
}

uint32_t AppStartResCmd::execute(AppCmdExec *app_cmd_exec)
{
    //执行一些退出流程，应该是调on.end函数
    app_cmd_exec->app_start_res(m_app_start_res_cmd.app_name, m_app_start_res_cmd.res);
    //Todo:
   /* uvManager* uv_manager = uvManagerFactor::get_uv_manager_instance();
    uv_manager->stop();
*/
    return 0;
}

std::string AppStartResCmd::serialize()
{
    std::stringstream ss;
    msgpack::pack(ss, m_app_start_res_cmd);
    return ss.str();
}

uint32_t AppStartResCmd::deserialize(std::string ss)
{
    //解码
    msgpack::object_handle oh = msgpack::unpack(ss.c_str(),ss.size());
    msgpack::object obj = oh.get();

    //将解码数据放到内部结构体中
    obj.convert(m_app_start_res_cmd);

    return 0;
}