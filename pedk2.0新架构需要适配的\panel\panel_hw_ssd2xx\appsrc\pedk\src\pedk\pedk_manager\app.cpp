#include "app.h"
#include "basic/os/os.h"

App::App()
{
    m_heartbeat = true;
}

void App::update_heartbeat()
{
    //更新此app的心跳时间
    m_os_information.m_hearbeat_time = OS::get_systime();
    //LOG_D("heartbeat time :%d",(int64_t)m_os_information.m_hearbeat_time);
}

std::string App:: get_start_time_by_string()
{
   return OS::time_to_string(&m_os_information.m_start_time);
}

void  App::kill_self()
{
    OS::kill_app(m_os_information);
}
