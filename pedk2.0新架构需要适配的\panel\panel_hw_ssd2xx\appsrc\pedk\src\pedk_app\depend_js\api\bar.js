/**
 * 本文件由PEDK API DSL 生成，请不要修改本文件
 */

import { defProp } from "../common/utils.js";

import { StyleSheet } from "./style-sheet.js";

export class Bar {
  constructor() {
    defProp(this, "id", { types: ["String"], default: "" });
    defProp(this, "type", { types: ["String"], default: "bar" });
    defProp(this, "x", { types: ["Int"], default: 0 });
    defProp(this, "y", { types: ["Int"], default: 0 });
    defProp(this, "w", { types: ["Int"], default: 0 });
    defProp(this, "h", { types: ["Int"], default: 0 });
    defProp(this, "style_sheet", { types: [StyleSheet] });
    defProp(this, "min", { types: ["Int"], default: 0 });
    defProp(this, "max", { types: ["Int"], default: 0 });
    defProp(this, "value", { types: ["Int"], default: 0 });
  }
}
