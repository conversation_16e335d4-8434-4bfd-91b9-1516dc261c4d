#ifndef _APP_CMD_PEDK_EXEC_H_
#define _APP_CMD_PEDK_EXEC_H_

#include "cmd/exec/app_cmd_exec.h"

/* PEDK进程的APP CMD指令的执行者 */
class AppCmdPedkExec: public AppCmdExec{
public:
    ~AppCmdPedkExec();

    void app_start_res(std::string app_name, bool res) override;
    void app_heart_beat(std::string app_name) override;
    void app_uplink_data(std::string target, std::string msg) override;
    void app_exit(std::string app_name) override;
    void app_reset(std::string app_name) override;
    void app_close_spec_app(std::string app_name)override;
    void app_reset_printer()override;
    void app_get_session_id(const std::string & app_name) override;
    static AppCmdPedkExec* get_instance(); 
protected:
private:
    AppCmdPedkExec();

    static AppCmdPedkExec *m_instance;
};

#endif // _APP_CMD_PEDK_EXEC_H_