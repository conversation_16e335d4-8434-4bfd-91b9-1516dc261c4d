#include <msgpack.hpp>

#include "transport/transport.h"

#include "pedk/pedk_cmd/pedk_cmd_context.h"
#include "pedk/pedk_cmd/pedk_cmd_pedk_exec.h"
#include "pedk/pedk_cmd/app_cmd_pedk_exec.h"

#include "cmd/cmds/cmd.h"
#include "cmd/cmds/pedk_cmds/pedk_cmds.h"
#include "cmd/cmds/pedk_cmds/ping_cmd.h"
#include "cmd/cmds/pedk_cmds/start_cmd.h"
#include "cmd/cmds/pedk_cmds/end_cmd.h"
#include "cmd/cmds/pedk_cmds/uplink_data_cmd.h"
#include "cmd/cmds/pedk_cmds/get_static_cmd.h"
#include "cmd/cmds/pedk_cmds/get_dynamic_cmd.h"
#include "cmd/cmds/pedk_cmds/get_app_info_cmd.h"
#include "cmd/cmds/pedk_cmds/error_info_cmd.h"
#include "cmd/cmds/app_cmds/app_heartbeat_cmd.h"
#include "cmd/cmds/app_cmds/app_uplink_data_cmd.h"
#include "cmd/cmds/app_cmds/app_start_res_cmd.h"
#include "cmd/cmds/app_cmds/app_exit_cmd.h"
#include "cmd/cmds/app_cmds/app_reset_cmd.h"
#include "cmd/cmds/app_cmds/app_close_spec_app_cmd.h"
#include "cmd/cmds/app_cmds/app_reset_printer_cmd.h"
#include "cmd/cmds/app_cmds/app_cmds.h"
#include "cmd/cmds/app_cmds/app_get_session_cmd.h"

PedkCmdContext* PedkCmdContext::m_instance = nullptr;


PedkCmdContext::PedkCmdContext()
{
}

PedkCmdContext::~PedkCmdContext()
{   
}

PedkCmdContext* PedkCmdContext::get_instance()
{
    if(m_instance == nullptr){
        m_instance = new PedkCmdContext;
    }

    return m_instance;
}

std::string PedkCmdContext::serialize()
{
    return nullptr;
}

/**
 * @brief PEDK主进程的一级解析流程
 *        主要目的是解析消息头，确认是哪条消息，然后进行二级解析
 * @param msg_stream 
 * @return int32_t 
 */
int32_t PedkCmdContext::deserialize(std::string &msg_stream)
{
    int32_t ret = -1;

    //解析头
    msgpack::object_handle oh = msgpack::unpack(msg_stream.data(), msg_stream.size());
    msgpack::object obj = oh.get();
   
    MsgHeader res;
    obj.convert(res);

    /*if(res.type != MSG_APP_HEART_BEAT){
        LOG_I("Pedk recv msg ,type = %d",res.type);
    }*/
    

    /* 消息type分为pedk cmd 和 app cmd两种，统一编址。
    认为小于等于MSG_RESET_PRINTER的为pedk cmd。大于MSG_RESET_PRINTER的为app cmd */
    if(res.type < MSG_APP_START_RES) {
        PEDKMsg(res.type);
        // 1 检查是否为pedk cmd
        PedkCmd *pedk_cmd = nullptr;
        switch(res.type){
            case MSG_PING:
                pedk_cmd = new PingCmd;
                break;
            case MSG_START:
                pedk_cmd = new StartCmd;
                break;
            case MSG_END:
                pedk_cmd = new EndCmd;
                break;
            case MSG_UPLINK_DATA:
                pedk_cmd = new UplinkDataCmd;
                break;
             case MSG_GET_STATIC_PROPERTY:
                pedk_cmd = new GetStaticCmd;
                break;
            case MSG_GET_DYNAMIC_PROPERTY:
                pedk_cmd = new GetDynamicCmd;
                break;
            case MSG_GET_APP_INFO:
                pedk_cmd = new GetAppInfoCmd;
                break;
            case MSG_ERROR_INFO:
                pedk_cmd = new ErrorInfoCmd;
                break;
            default:
                pedk_cmd = nullptr;
                break;
        }
        // 1.1 如果是pedk cmd指令，则创建PedkCmdPedkExec执行实例
        if(pedk_cmd != nullptr ){
            if(0 == pedk_cmd->deserialize(msg_stream)){
                PedkCmdExec *pedk_cmd_exec =(PedkCmdExec*) PedkCmdPedkExec::get_instance();
                pedk_cmd->execute(pedk_cmd_exec);
                ret = 0;
            }
            delete pedk_cmd;
        }
    }else{
        PEDKMsg(res.type);

        AppCmd *app_cmd = nullptr;
       
        // 2 检查是否为app cmd
        switch(res.type){
            case MSG_APP_START_RES:
                app_cmd = new AppStartResCmd;
                break;
            case MSG_APP_UPLINK_DATA:
                app_cmd = new AppUplinkDataCmd;
                break;
            case MSG_APP_HEART_BEAT:
                app_cmd = new AppHeartBeatCmd;
                break;
            case MSG_APP_EXIT:
                app_cmd = new AppExitCmd;
                break;
            case MSG_APP_RESET:
                app_cmd = new AppResetCmd;
                break;
            case MSG_APP_CLOSE_SPEC_APP:
                app_cmd = new AppCloseSpecAppCmd;
                break;
            case MSG_APP_RESET_PRINTER:
                app_cmd = new AppResetCmd;
                break;
            case MSG_GET_SESSION_ID:
                app_cmd = new AppGetSessionCmd;
                break;
            default:
                app_cmd = nullptr;
                break;
        }

        // 2.1 如果是pedk cmd指令，则创建PedkCmdPedkExec执行实例
        if(app_cmd != nullptr ){
            if(0 == app_cmd->deserialize(msg_stream)){
                AppCmdExec *app_cmd_exec =(AppCmdExec*) AppCmdPedkExec::get_instance();
                app_cmd->execute(app_cmd_exec);
                ret = 0;
            }
            delete app_cmd;
        }
    }

    return ret;
}

const char *head = ">>>>>>";
const char *tail = "<<<<<<";
void PedkCmdContext::PEDKMsg(int type)
{
    //输入指令
    switch(type){
    case MSG_PING:
        LOG_I("\n%sMSG_PING%s",head,tail);
        break;
    case MSG_START:
        LOG_I("\n%sMSG_START%s",head,tail);
        break;
    case MSG_END:
        LOG_I("\n%sMSG_END%s",head,tail);
        break;
    case MSG_UPLINK_DATA:
        LOG_I("\n%sMSG_UPLINK_DATA%s",head,tail);
        break;
    case MSG_GET_STATIC_PROPERTY:
        LOG_I("\n%sMSG_GET_STATIC_PROPERTY%s",head,tail);
        break;
    case MSG_GET_DYNAMIC_PROPERTY:
        LOG_I("\n%sMSG_GET_DYNAMIC_PROPERTY%s",head,tail);
        break;
    case MSG_GET_APP_INFO:
        LOG_I("\n%sMSG_GET_APP_INFO%s",head,tail);
        break;
    case MSG_ERROR_INFO:
        LOG_I("\n%sMSG_ERROR_INFO%s",head,tail);
        break;
    case MSG_APP_START_RES:
        LOG_I("\n%sMSG_APP_START_RES%s",head,tail);
        break;
    case MSG_APP_UPLINK_DATA:
        LOG_I("\n%sMSG_APP_UPLINK_DATA%s",head,tail);
        break;
    case MSG_APP_HEART_BEAT:
        break;
    case MSG_APP_EXIT:
        LOG_I("\n%sMSG_APP_EXIT%s",head,tail);
        break;
    case MSG_APP_RESET:
        LOG_I("\n%sMSG_APP_RESET%s",head,tail);
        break;
    case MSG_APP_CLOSE_SPEC_APP:
        LOG_I("\n%sMSG_APP_CLOSE_SPEC_APP%s",head,tail);
        break;
    case MSG_APP_RESET_PRINTER:
        LOG_I("\n%sMSG_APP_RESET_PRINTER%s",head,tail);
        break;
  }

  //输出指令
  switch(type){
    case MSG_PING_RES:
        LOG_I("\n%sMSG_PING_RES%s",tail,head);
        break;
    case MSG_START_RES:
        LOG_I("\n%sMSG_START_RES%s",tail,head);
        break;
    case MSG_END_RES:
        LOG_I("\n%sMSG_END_RES%s",tail,head);
        break;
    case MSG_DOWNLINK_DATA:
        LOG_I("\n%sMSG_DOWNLINK_DATA%s",tail,head);
        break;
    case MSG_RET_STATIC_PROPERTY:
        LOG_I("\n%sMSG_RET_STATIC_PROPERTY%s",tail,head);
        break;
    case MSG_RET_DYNAMIC_PROPERTY:
        LOG_I("\n%sMSG_RET_DYNAMIC_PROPERTY%s",tail,head);
        break;
    case MSG_RESET_PRINTER:
        LOG_I("\n%sMSG_RESET_PRINTER%s",tail,head);
        break;
    case MSG_RET_APP_INFO:
        LOG_I("\n%sMSG_RET_APP_INFO%s",tail,head);
        break;
    case MSG_APP_END:
        LOG_I("\n%sMSG_APP_END%s",tail,head);
        break;
    case MSG_APP_UPLINK_DATA:
        LOG_I("\n%sMSG_APP_UPLINK_DATA%s",tail,head);
        break;
    case MSG_APP_FRONT:
        LOG_I("\n%sMSG_APP_FRONT%s",tail,head);
        break;
    case MSG_APP_BACK:
        LOG_I("\n%sMSG_APP_BACK%s",tail,head);
        break;
    case MSG_APP_EXIT:
        LOG_I("\n%sMSG_APP_EXIT%s",tail,head);
        break;
    case MSG_APP_RESET:
        LOG_I("\n%sMSG_APP_RESET%s",tail,head);
        break;
    case MSG_APP_SYS_ERR:
        LOG_I("\n%sMSG_APP_SYS_ERR%s",tail,head);
        break;
  }
}
