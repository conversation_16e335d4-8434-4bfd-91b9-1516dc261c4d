#include "cmd/cmds/pedk_cmds/ping_cmd.h"

PingCmd::PingCmd()
{
    m_ping_cmd.type = MSG_PING;
}

PingCmd::~PingCmd()
{
}

uint32_t PingCmd::execute(PedkCmdExec *pedk_cmd_exec)
{
    pedk_cmd_exec->ping();
    return 0;
}

std::string PingCmd::serialize()
{
    std::stringstream ss;
    msgpack::pack(ss, m_ping_cmd);

    return ss.str();
}

uint32_t PingCmd::deserialize(std::string ss)
{
    //解码
    msgpack::object_handle oh = msgpack::unpack(ss.c_str(),ss.size());
    msgpack::object obj = oh.get();

    //将解码数据放到内部结构体中
    obj.convert(m_ping_cmd);

    return 0;
}