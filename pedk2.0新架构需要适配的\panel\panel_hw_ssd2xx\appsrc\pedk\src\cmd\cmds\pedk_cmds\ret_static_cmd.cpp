#include "ret_static_cmd.h"

RetStaticCmd::RetStaticCmd()
{
    m_ret_static_cmd.type = MSG_RET_STATIC_PROPERTY;
    m_ret_static_cmd.app_name = "";
    m_ret_static_cmd.msg = "";
}

RetStaticCmd::RetStaticCmd(std::string app_name, std::string msg)
{
    m_ret_static_cmd.type = MSG_RET_STATIC_PROPERTY;
    m_ret_static_cmd.app_name = app_name;
    m_ret_static_cmd.msg = msg;
}

RetStaticCmd::~RetStaticCmd()
{
}

uint32_t RetStaticCmd::execute(PedkCmdExec *pedk_cmd_exec)
{
    pedk_cmd_exec->ret_static_property(m_ret_static_cmd.app_name, m_ret_static_cmd.msg);
   
    return 0;
}

std::string RetStaticCmd::serialize()
{
    std::stringstream ss;
    msgpack::pack(ss, m_ret_static_cmd);

    return ss.str();
}

uint32_t RetStaticCmd::deserialize(std::string ss)
{
    // 解码
    msgpack::object_handle oh = msgpack::unpack(ss.c_str(), ss.size());
    msgpack::object obj = oh.get();

    // 将解码数据放到内部结构体中
    obj.convert(m_ret_static_cmd);

    return 0;
}