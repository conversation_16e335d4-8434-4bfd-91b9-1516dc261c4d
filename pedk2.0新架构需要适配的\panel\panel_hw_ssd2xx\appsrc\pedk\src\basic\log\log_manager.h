#ifndef _LOG_MANAGER_H_
#define _LOG_MANAGER_H_

#include "basic/log/log.h"

/* LOG 管理类 */
/* 主要目的为代码提供LOG宏定义，供全局使用。
    （1）LOG管理类会根据当前设置的log type及log level决定是否输出、以何种方式输出、输出到哪里。
 */
class LogManager {
public:
    ~LogManager();
    static LogManager* get_instance();

    void set_log(Log* log);
    Log* get_log();
protected:
private:
    LogManager();
    static LogManager *m_instance;
    Log* m_log;    //此类最重要参数。不同的进程会聚合不同的log实例，从而执行不同的具体动作。
};


#define LOG(level, func, format, ...) LogManager::get_instance()->get_log()->log_out(level, func, format, ##__VA_ARGS__);
#define LOG2(level, func, format, ...) LogManager::get_instance()->get_log()->log_out2(level, func, format, ##__VA_ARGS__);

#define LOG_D(format, ...) LOG(PEDK_LOG_LEVEL_DEBUG, __func__, format, ##__VA_ARGS__)
#define LOG_I(format, ...) LOG(PEDK_LOG_LEVEL_INFO, __func__, format, ##__VA_ARGS__)
#define LOG_W(format, ...) LOG(PEDK_LOG_LEVEL_WARNING, __func__, format, ##__VA_ARGS__)
#define LOG_E(format, ...) LOG(PEDK_LOG_LEVEL_ERROR, __func__, format, ##__VA_ARGS__)
#define LOG_F(format, ...) LOG(PEDK_LOG_LEVEL_FATAL, __func__, format, ##__VA_ARGS__)
#define LOG_JS(format, ...) LOG2(PEDK_LOG_LEVEL_INFO, __func__, format, ##__VA_ARGS__)

#endif // _LOG_MANAGER_H_