#ifndef _GET_STATIC_CMD_H_
#define _GET_STATIC_CMD_H_

#include "cmd/cmds/pedk_cmds/pedk_cmds.h"
#include "cmd/exec/pedk_cmd_exec.h"

typedef struct GetStaticCmdFormat {
    uint32_t type; // 指令类型
    std::string app_name;

    MSGPACK_DEFINE(type,app_name);
} GetStaticCmdFormat;

class GetStaticCmd : public PedkCmd {
public:
    GetStaticCmd();
    GetStaticCmd(std::string app_name);
    ~GetStaticCmd();
    uint32_t execute(PedkCmdExec *pedk_cmd_exec) override;
    std::string serialize() override;
    uint32_t deserialize(std::string ss) override;
protected:
private:
    GetStaticCmdFormat m_get_static_cmd;
};

#endif // _GET_STATIC_CMD_H_