/**
 * 本文件由PEDK API DSL 生成，请不要修改本文件
 */

import { isType } from "../common/utils.js";

import {
  Audit_Impt,
  setAuditEnable_Impt,
  setAuditDisable_Impt,
} from "../implement/index.js";

/**
 * Audit feature class
 * @class Audit
 */
export class Audit {
  constructor(protocol, server_ip, server_path, name, password, port, type) {
    if (
      !isType(protocol, "Number") ||
      !isType(server_ip, "String") ||
      !isType(server_path, "String") ||
      !isType(name, "String") ||
      !isType(password, "String") ||
      !isType(port, "Number") ||
      !isType(type, "Number")
    ) {
      return "EINVALIDPARAM";
    }
    this._instance = new Audit_Impt(
      protocol,
      server_ip,
      server_path,
      name,
      password,
      port,
      type
    );
  }
}

export function setAuditEnable(audit) {
  if (!isType(audit, Audit)) {
    return "EINVALIDPARAM";
  }
  return setAuditEnable_Impt(audit);
}

export function setAuditDisable(no) {
  if (!isType(no, "Number")) {
    return "EINVALIDPARAM";
  }
  return setAuditDisable_Impt(no);
}
