/**
 * 本文件由PEDK API DSL 生成，请不要修改本文件
 */

import { isType } from "../common/utils.js";

import {
  getSystemCapabilitiesList_Impt,
  getSupportedPrintPaperTray_Impt,
  getSupportedPrintPaperSize_Impt,
  getSupportedPrintMediaType_Impt,
  getSupportedScanPaperSize_Impt,
} from "../implement/index.js";

export function getSystemCapabilitiesList() {
  return getSystemCapabilitiesList_Impt();
}

export function getSupportedPrintPaperTray() {
  return getSupportedPrintPaperTray_Impt();
}

export function getSupportedPrintPaperSize(tray) {
  if (!isType(tray, "String")) {
    return "EINVALIDPARAM";
  }
  return getSupportedPrintPaperSize_Impt(tray);
}

export function getSupportedPrintMediaType(tray) {
  if (!isType(tray, "String")) {
    return "EINVALIDPARAM";
  }
  return getSupportedPrintMediaType_Impt(tray);
}

export function getSupportedScanPaperSize() {
  return getSupportedScanPaperSize_Impt();
}
