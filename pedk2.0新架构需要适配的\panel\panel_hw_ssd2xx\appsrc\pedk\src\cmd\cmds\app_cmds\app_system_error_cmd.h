#ifndef _APP_SYSTEM_ERROR_CMD_H_
#define _APP_SYSTEM_ERROR_CMD_H_

#include "cmd/cmds/app_cmds/app_cmds.h"
#include "cmd/exec/app_cmd_exec.h"

typedef struct AppSystemErrorCmdFormat {
    uint32_t type; // 指令类型
    std::string error_type; //错误类型
    std::string error_info;//错误内容

    MSGPACK_DEFINE(type, error_type, error_info);
} AppSystemErrorCmdFormat;

class AppSystemErrorCmd: public AppCmd{
public:
    AppSystemErrorCmd();
    AppSystemErrorCmd(std::string error_type,std::string error_info);
    ~AppSystemErrorCmd();
    uint32_t execute(AppCmdExec *app_cmd_exec) override;
    std::string serialize() override;
    uint32_t deserialize(std::string ss) override;
protected:
private:
    AppSystemErrorCmdFormat m_app_system_error_cmd;
};

#endif // _APP_SYSTEM_ERROR_CMD_H_
