/**
 * 本文件由PEDK API DSL 生成，请不要修改本文件
 */

import { isType } from "../common/utils.js";

import {
  CardListener_Impt,
  addCreditCardListener_Impt,
  removeCreditCardListener_Impt,
} from "../implement/index.js";

/**
 * Card control class
 * @class CardListener
 */
export class CardListener {
  constructor() {
    this._instance = new CardListener_Impt();
  }

  // notify(card_status, card_num) {
  //   if (!isType(card_num, "String")) {
  //     return "EINVALIDPARAM";
  //   }
  //   if (!isType(card_status, "String")) {
  //     return "EINVALIDPARAM";
  //   }
  //   return this._instance.notify(card_status, card_num);
  // }
  
  notify(card_num) {
    if (!isType(card_num, "String")) {
      return "EINVALIDPARAM";
    }

    return this._instance.notify( card_num);
  }
}

export function addCreditCardListener(listener) {
  if (!isType(listener, CardListener)) {
    return "EINVALIDPARAM";
  }
  return addCreditCardListener_Impt(listener);
}

export function removeCreditCardListener(listener) {
  if (!isType(listener, CardListener)) {
    return "EINVALIDPARAM";
  }
  return removeCreditCardListener_Impt(listener);
}
