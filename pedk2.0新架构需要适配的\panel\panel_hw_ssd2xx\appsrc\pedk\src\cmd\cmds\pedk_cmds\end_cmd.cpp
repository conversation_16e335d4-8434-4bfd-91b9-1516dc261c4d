#include "end_cmd.h"
//#include "transport/transport_pedk.h"
//#include "app_end_cmd.h"
//#include "basic/os/os.h"
//#include "manager/app_manager/app_manager.h"
//#include "manager/app_manager/app.h"

EndCmd::EndCmd()
{
    m_end_cmd.type = MSG_END;
    m_end_cmd.app_name = "";
}

EndCmd::EndCmd(std::string app_name)
{
    m_end_cmd.type = MSG_END;
    m_end_cmd.app_name = app_name;
}

EndCmd::~EndCmd()
{
}

uint32_t EndCmd::execute(PedkCmdExec *pedk_cmd_exec)
{
    pedk_cmd_exec->end(m_end_cmd.app_name);
    
    return 0;
}

std::string EndCmd::serialize()
{
    std::stringstream ss;
    msgpack::pack(ss, m_end_cmd);
    return ss.str();
}

uint32_t EndCmd::deserialize(std::string ss)
{
    //解码
    msgpack::object_handle oh = msgpack::unpack(ss.c_str(),ss.size());
    msgpack::object obj = oh.get();

    //将解码数据放到内部结构体中
    obj.convert(m_end_cmd);

    return 0;
}