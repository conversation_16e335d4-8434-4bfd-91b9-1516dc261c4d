#ifndef _START_CMD_H_
#define _START_CMD_H_

#include "cmd/cmds/pedk_cmds/pedk_cmds.h"
#include "cmd/exec/pedk_cmd_exec.h"

typedef struct StartCmdFormat {
    uint32_t type; // 指令类型
    std::string app_name; // APP

    MSGPACK_DEFINE(type, app_name);
} StartCmdFormat;

class StartCmd: public PedkCmd{
public:
    StartCmd();
    StartCmd(std::string app_name);
    ~StartCmd();
    uint32_t execute(PedkCmdExec *pedk_cmd_exec) override;
    std::string serialize() override;
    uint32_t deserialize(std::string ss) override;
protected:
private:
    StartCmdFormat m_start_cmd;
};

#endif // _START_CMD_H_