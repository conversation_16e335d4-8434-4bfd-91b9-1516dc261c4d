#ifndef _CONFIG_H_
#define _CONFIG_H_

// 全局引用头文件
#include <stdint.h>
#include <stdio.h>
#include <fstream>

//根据机种选择配置头文件
#ifdef SIMULATOR
#include "config/config_develop.h"
#elif MT7125
#include "config/config_7125.h"
#else
#include "config/config.h"
#endif

class Config {
public:
    ~Config();
    static Config* get_instance();

    // 内部变量读写
    void set_app_name(const char* app_name);
    std::string get_app_name();

protected:
private:
    static Config* m_instance; // 单例

    std::string m_app_name;
    Config();
};

#include "basic/log/log_manager.h"

#endif // _CONFIG_H_
