/**
 * 本文件由PEDK API DSL 生成，请不要修改本文件
 */

import { isType } from "../common/utils.js";

import { Beeper_Impt } from "../implement/index.js";

/**
 *
 * @class Beeper
 */
export class Beeper {
  getStatus() {
    return this._instance.getStatus();
  }

  start() {
    return this._instance.start();
  }

  stop() {
    return this._instance.stop();
  }

  setVolume(volume) {
    if (!isType(volume, "Number")) {
      return "EINVALIDPARAM";
    }
    return this._instance.setVolume(volume);
  }

  getVolume() {
    return this._instance.getVolume();
  }

  getDuration() {
    return this._instance.getDuration();
  }

  setDuration(duration) {
    if (!isType(duration, "Number")) {
      return "EINVALIDPARAM";
    }
    return this._instance.setDuration(duration);
  }

  getFrequency() {
    return this._instance.getFrequency();
  }

  setFrequency(freq) {
    if (!isType(freq, "Number")) {
      return "EINVALIDPARAM";
    }
    return this._instance.setFrequency(freq);
  }
}
