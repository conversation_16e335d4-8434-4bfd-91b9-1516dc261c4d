#ifndef _PEDK_CMD_CONTEXT_H_
#define _PEDK_CMD_CONTEXT_H_

#include "cmd/context/cmd_context.h"
#include "cmd/exec/pedk_cmd_exec.h"
#include "cmd/exec/app_cmd_exec.h"

class PedkCmdContext :public CmdContext{
public:
    ~PedkCmdContext();
    std::string serialize() override;
    int32_t deserialize(std::string &msg_stream) override;

    static PedkCmdContext* get_instance();
    static void PEDKMsg(int type);

protected:
private:
    PedkCmdContext();
    static PedkCmdContext *m_instance;
    
};

#endif // _PEDK_CMD_CONTEXT_H_