#ifndef _APP_DOWNLINK_DATA_CMD_H_
#define _APP_DOWNLINK_DATA_CMD_H_

#include "cmd/cmds/app_cmds/app_cmds.h"
#include "cmd/exec/app_cmd_exec.h"

typedef struct AppDownlinkDataCmdFormat {
    uint32_t type;        // 指令类型
    std::string msg;      // 消息

    MSGPACK_DEFINE(type, msg);
} AppDownlinkDataCmdFormat;

class AppDownlinkDataCmd: public AppCmd{
public:
    AppDownlinkDataCmd();
    AppDownlinkDataCmd(std::string msg);
    ~AppDownlinkDataCmd();
    uint32_t execute(AppCmdExec *app_cmd_exec) override;
    std::string serialize() override;
    uint32_t deserialize(std::string ss) override;
    std::string get_msg();
protected:
private:
    AppDownlinkDataCmdFormat m_app_downlink_data_cmd;
};


#endif // _APP_DOWNLINK_DATA_CMD_H_
