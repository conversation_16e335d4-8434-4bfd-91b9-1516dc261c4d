#ifndef _PEDK_CMD_EXEC_H_
#define _PEDK_CMD_EXEC_H_

//PEDK主进程与PEDK客户端进程之间使用 PEDK CMD指令。
//此为PEDK系列指令执行动作的父类。
//同样的指令，在PEDK主进程端和PEDK客户端执行的动作不同（即：一端有动作一端没动作）
#include "basic/log/log_manager.h"

class PedkCmdExec {
public:
    PedkCmdExec(){};
    ~PedkCmdExec(){};
    
    virtual void ping(){};
    virtual void ping_res(bool certificate_result){};
    virtual void start(std::string app_name){};
    virtual void start_res(std::string app_name,bool res){};
    virtual void end(std::string app_name){};
    virtual void end_res(std::string app_name,bool res){};
    virtual void uplink_data(std::string target_app_name, std::string msg){};
    virtual void downlink_data(std::string msg){};
    virtual void get_static_property(std::string app_name){};
    virtual void ret_static_property(std::string app_name, std::string msg){};
    virtual void get_dynamic_property(){};
    virtual void ret_dynamic_property(std::string app_name, std::string start_time){};
    virtual void reset_printer(){};
    virtual void get_app_info(){};
    virtual void ret_app_info_root_app(std::string root_app_name){};
    virtual void ret_app_info(std::string app_name, bool autoboot, bool display_icon, std::string icon_path, const std::string display_name){};
    virtual void error_info(std::string err_type, std::string err_info){};

protected:
private:
    
};

#endif // _PEDK_CMD_EXEC_H_