#include "ret_app_info_cmd.h"

RetAppInfoCmd::RetAppInfoCmd(RetAppInfoCmdFormat ret_app_info_cmd)
{
    m_ret_app_info_cmd = ret_app_info_cmd;
    m_ret_app_info_cmd.type = MSG_RET_APP_INFO;
}

RetAppInfoCmd::RetAppInfoCmd()
{
    m_ret_app_info_cmd.type = MSG_RET_APP_INFO;
}

RetAppInfoCmd::~RetAppInfoCmd()
{
}

uint32_t RetAppInfoCmd::execute(PedkCmdExec *pedk_cmd_exec)
{
    std::cout<<"RetAppInfoCmd execute"<<std::endl;
    pedk_cmd_exec->ret_app_info_root_app(m_ret_app_info_cmd.root_app_name);
    for(AppInfo it : m_ret_app_info_cmd.apps_info){
        pedk_cmd_exec->ret_app_info(it.app_name, it.is_autoboot, it.display_icon, it.icon_path, it.display_name);
    }

    return 0;
}

std::string RetAppInfoCmd::serialize()
{
    std::stringstream ss;
    msgpack::pack(ss, m_ret_app_info_cmd);
    return ss.str();
}

uint32_t RetAppInfoCmd::deserialize(std::string ss)
{
    //解码
    msgpack::object_handle oh = msgpack::unpack(ss.c_str(),ss.size());
    msgpack::object obj = oh.get();

    //将解码数据放到内部结构体中
    obj.convert(m_ret_app_info_cmd);

    return 0;
}