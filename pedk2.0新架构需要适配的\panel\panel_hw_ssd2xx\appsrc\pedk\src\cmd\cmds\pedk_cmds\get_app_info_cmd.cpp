#include "get_app_info_cmd.h"

GetAppInfoCmd::GetAppInfoCmd()
{
    m_get_app_info_cmd.type = MSG_GET_APP_INFO;
}

GetAppInfoCmd::~GetAppInfoCmd()
{
}

uint32_t GetAppInfoCmd::execute(PedkCmdExec *pedk_cmd_exec)
{
    pedk_cmd_exec->get_app_info();

    return 0;
}

std::string GetAppInfoCmd::serialize()
{
    std::stringstream ss;
    msgpack::pack(ss, m_get_app_info_cmd);
    return ss.str();
}

uint32_t GetAppInfoCmd::deserialize(std::string ss)
{
    //解码
    msgpack::object_handle oh = msgpack::unpack(ss.c_str(),ss.size());
    msgpack::object obj = oh.get();

    //将解码数据放到内部结构体中
    obj.convert(m_get_app_info_cmd);

    return 0;
}