#ifndef _END_CMD_H_
#define _END_CMD_H_

#include "cmd/cmds/pedk_cmds/pedk_cmds.h"
#include "cmd/exec/pedk_cmd_exec.h"

typedef struct EndCmdFormat {
    uint32_t type; // 指令类型
    std::string app_name; // APP

    MSGPACK_DEFINE(type, app_name);
} EndCmdFormat;

class EndCmd: public PedkCmd{
public:
    EndCmd();
    EndCmd(std::string app_name);
    ~EndCmd();
    uint32_t execute(PedkCmdExec *pedk_cmd_exec) override;
    std::string serialize() override;
    uint32_t deserialize(std::string ss) override;
protected:
private:
    EndCmdFormat m_end_cmd;
};

#endif // _END_CMD_H_