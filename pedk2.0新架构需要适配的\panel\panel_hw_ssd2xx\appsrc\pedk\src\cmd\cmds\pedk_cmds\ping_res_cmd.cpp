#include "cmd/cmds/pedk_cmds/ping_res_cmd.h"

PingResCmd::PingResCmd()
{
    m_ping_res_cmd.type = MSG_PING_RES;
    m_ping_res_cmd.certificate_result = false;
}

PingResCmd::PingResCmd(bool certificate_result)
{
    m_ping_res_cmd.type = MSG_PING_RES;
    m_ping_res_cmd.certificate_result = certificate_result;
}

PingResCmd::~PingResCmd()
{
}

uint32_t PingResCmd::execute(PedkCmdExec *pedk_cmd_exec)
{
    pedk_cmd_exec->ping_res(m_ping_res_cmd.certificate_result);
    
    return 0;
}

std::string PingResCmd::serialize()
{
    std::stringstream ss;
    msgpack::pack(ss, m_ping_res_cmd);

    return ss.str();
}

uint32_t PingResCmd::deserialize(std::string ss)
{
    //解码
    msgpack::object_handle oh = msgpack::unpack(ss.c_str(),ss.size());
    msgpack::object obj = oh.get();

    //将解码数据放到内部结构体中
    obj.convert(m_ping_res_cmd);

    return 0;
}