#include "app_front_cmd.h"

AppFrontCmd::AppFrontCmd()
{
    m_app_front_cmd.type = MSG_APP_FRONT;
}


AppFrontCmd::~AppFrontCmd()
{
}

uint32_t AppFrontCmd::execute(AppCmdExec *app_cmd_exec)
{
    //执行切换到前端流程
    app_cmd_exec->app_front();

    return 0;
}

std::string AppFrontCmd::serialize()
{
    std::stringstream ss;
    msgpack::pack(ss, m_app_front_cmd);
    return ss.str();
}

uint32_t AppFrontCmd::deserialize(std::string ss)
{
    //解码
    msgpack::object_handle oh = msgpack::unpack(ss.c_str(),ss.size());
    msgpack::object obj = oh.get();

    //将解码数据放到内部结构体中
    obj.convert(m_app_front_cmd);

    return 0;
}