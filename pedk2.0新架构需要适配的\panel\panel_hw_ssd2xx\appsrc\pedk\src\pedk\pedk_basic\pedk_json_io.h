#ifndef _PEDK_JSON_IO_H_
#define _PEDK_JSON_IO_H_
#include "basic/config.h"
#include "basic/json_io/json_io.h"
#include "pedk/pedk_manager/app.h"
#include "pedk/pedk_manager/app_manager.h"

/* pedk进程独有的json io操作 */
class PedkJsonIO:public JsonIO {
public:
    static bool get_autoboot(std::string json_str);         //从app的projec.config.json文件中，获取自启动信息
    static int64_t get_starttime(std::string json_str);     //从app的projec.config.json文件中，获取开始时间
    static int64_t get_endtime(std::string json_str);       //从app的projec.config.json文件中，获取结束时间


    static UserAppConfig get_user_app_config_json(std::string json_str);                    //加载app.config.json配置文件全部信息  
protected:
private:
    
};

#endif // _PEDK_JSON_IO_H_