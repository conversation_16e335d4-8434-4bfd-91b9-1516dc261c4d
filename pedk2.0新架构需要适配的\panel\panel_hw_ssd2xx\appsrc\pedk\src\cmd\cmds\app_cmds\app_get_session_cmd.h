#ifndef APP_GET_SESSION_CMD_H
#define APP_GET_SESSION_CMD_H


#include "cmd/cmds/app_cmds/app_cmds.h"
#include "cmd/exec/app_cmd_exec.h"

typedef struct AppGetSessionCmdFormat {
    uint32_t type; // 指令类型
    std::string app_name;
    
    MSGPACK_DEFINE(type, app_name);
} AppGetSessionCmdFormat;

class AppGetSessionCmd: public AppCmd{
public:
    AppGetSessionCmd(const std::string& app_name);
    AppGetSessionCmd();
    ~AppGetSessionCmd() = default;
    uint32_t execute(AppCmdExec *app_cmd_exec) override;
    std::string serialize() override;
    uint32_t deserialize(std::string ss) override;
protected:
private:
    AppGetSessionCmdFormat m_get_session_cmd;
};


#endif // APP_GET_SESSION_CMD_H
