#ifndef _RET_APP_INFO_CMD_H_
#define _RET_APP_INFO_CMD_H_

#include "cmd/cmds/pedk_cmds/pedk_cmds.h"
#include "cmd/exec/pedk_cmd_exec.h"

typedef struct AppInfo {
    std::string app_name; // 应用名
    bool is_autoboot;     // 是否自启动
    bool display_icon;    // 是否显示图标
    std::string icon_path;// 图标路径
    std::string display_name; //app显示名
    
    MSGPACK_DEFINE(app_name, is_autoboot, display_icon, icon_path, display_name);
} AppInfo;

typedef struct RetAppInfoCmdFormat {
    uint32_t type;        // 指令类型
    std::vector<AppInfo> apps_info;
    std::string root_app_name;

    MSGPACK_DEFINE(type, apps_info,root_app_name);
} RetAppInfoCmdFormat;

class RetAppInfoCmd: public PedkCmd{
public:
    RetAppInfoCmd(RetAppInfoCmdFormat ret_app_info_cmd);
    RetAppInfoCmd();
    ~RetAppInfoCmd();
    uint32_t execute(PedkCmdExec *pedk_cmd_exec) override;
    std::string serialize() override;
    uint32_t deserialize(std::string ss) override;
protected:
private:
    RetAppInfoCmdFormat m_ret_app_info_cmd;
};

#endif // _RET_ICON_INFO_CMD_H_