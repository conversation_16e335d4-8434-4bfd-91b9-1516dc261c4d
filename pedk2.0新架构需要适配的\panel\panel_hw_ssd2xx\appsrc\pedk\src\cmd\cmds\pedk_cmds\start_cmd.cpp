#include "start_cmd.h"

StartCmd::StartCmd()
{
    m_start_cmd.type = MSG_START;
    m_start_cmd.app_name = "";
}

StartCmd::StartCmd(std::string app_name)
{
    m_start_cmd.type = MSG_START;
    m_start_cmd.app_name = app_name;
}

StartCmd::~StartCmd()
{
}

uint32_t StartCmd::execute(PedkCmdExec *pedk_cmd_exec)
{
    pedk_cmd_exec->start(m_start_cmd.app_name);

    return 0;
}

std::string StartCmd::serialize()
{
    std::stringstream ss;
    msgpack::pack(ss, m_start_cmd);
    return ss.str();
}

uint32_t StartCmd::deserialize(std::string ss)
{
    //解码
    msgpack::object_handle oh = msgpack::unpack(ss.c_str(),ss.size());
    msgpack::object obj = oh.get();

    //将解码数据放到内部结构体中
    obj.convert(m_start_cmd);

    std::cout << m_start_cmd.app_name << std::endl;

    return 0;
}