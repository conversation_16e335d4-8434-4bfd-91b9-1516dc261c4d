#include <atomic>


class SessionGenerator {
public:
    static SessionGenerator& get_instance() {
        static SessionGenerator instance;
        return instance;
    }
    uint32_t getNextSessionId() {
        if (m_session_id >= 0xffffffff)
        {
            m_session_id = 0;
        } 
        return m_session_id.fetch_add(1, std::memory_order_relaxed);   
    }
private: 
    SessionGenerator(): m_session_id(1) {}
    std::atomic<uint32_t> m_session_id;    
};


