# PEDK IPC 绯荤粺娣卞害鍒嗘瀽鎶ュ憡

## 1. 绯荤粺鏋舵瀯姒傝

PEDK (Platform Embedded Development Kit) IPC绯荤粺閲囩敤鍒嗗眰鏋舵瀯璁捐锛屽疄鐜颁簡JavaScript搴旂敤涓庡簳灞傜‖浠朵箣闂寸殑楂樻晥閫氫俊銆�

### 1.1 鏋舵瀯灞傛
- **搴旂敤灞�**: JavaScript PEDK App銆丳anel UI銆乄eb Interface
- **杩愯鏃跺眰**: QuickJS寮曟搸銆丅ridge妯″潡銆佷簨浠堕槦鍒�
- **绠＄悊灞�**: PEDK Manager銆丒vent Manager銆丮essage Router
- **鎶借薄灞�**: QIO妗嗘灦銆丱SAL鎿嶄綔绯荤粺鎶借薄灞�
- **搴曞眰IPC**: 鍏变韩鍐呭瓨銆佹秷鎭槦鍒椼€丼ocket銆佷俊鍙烽噺绛�

### 1.2 鏁翠綋鏋舵瀯鍥�

```
PEDK IPC 绯荤粺鏁翠綋鏋舵瀯 (5灞傛灦鏋勮璁�)
鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺�

鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹�                           馃數 搴旂敤灞� (Application Layer)                         鈹�
鈹溾攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹�  JavaScript PEDK App    鈹�    Panel UI Interface    鈹�    Web Interface           鈹�
鈹�  鈥� 涓氬姟閫昏緫澶勭悊          鈹�    鈥� 鐢ㄦ埛浜や簰鐣岄潰         鈹�    鈥� 杩滅▼璁块棶鎺ュ彛           鈹�
鈹�  鈥� 搴旂敤鐢熷懡鍛ㄦ湡          鈹�    鈥� 闈㈡澘浜嬩欢澶勭悊         鈹�    鈥� HTTP/WebSocket鏈嶅姟     鈹�
鈹�  鈥� 浜嬩欢鍝嶅簲澶勭悊          鈹�    鈥� 鏄剧ず鐘舵€佹洿鏂�         鈹�    鈥� RESTful API           鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
                                        鈫曪笍 JavaScript 鈫� C 璋冪敤
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹�                         馃煟 PEDK杩愯鏃跺眰 (Runtime Layer)                         鈹�
鈹溾攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹�    QuickJS Engine       鈹�     Bridge Module        鈹�   Event Queue & Runtime    鈹�
鈹�  鈥� JavaScript瑙ｉ噴鎵ц   鈹�    鈥� C 鈫� JS 鏁版嵁杞崲     鈹�    鈥� 娑堟伅闃熷垪绠＄悊           鈹�
鈹�  鈥� 鍐呭瓨绠＄悊涓嶨C         鈹�    鈥� 鍑芥暟璋冪敤妗ユ帴         鈹�    鈥� 浜嬩欢寰幆澶勭悊           鈹�
鈹�  鈥� 寮傚父澶勭悊鏈哄埗          鈹�    鈥� 寮傛鍥炶皟澶勭悊         鈹�    鈥� 搴旂敤瀹炰緥绠＄悊           鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
                                        鈫曪笍 娑堟伅璺敱涓庣鐞�
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹�                          馃煝 绠＄悊灞� (Management Layer)                           鈹�
鈹溾攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹�    PEDK Manager         鈹�    Event Manager         鈹�  Message Router & Service  鈹�
鈹�  鈥� 绯荤粺璧勬簮鍗忚皟          鈹�    鈥� 浜嬩欢鍒嗗彂澶勭悊         鈹�    鈥� 娑堟伅璺敱鍒嗗彂           鈹�
鈹�  鈥� 妯″潡娉ㄥ唽绠＄悊          鈹�    鈥� 鍥炶皟鍑芥暟绠＄悊         鈹�    鈥� 鏈嶅姟鍙戠幇娉ㄥ唽           鈹�
鈹�  鈥� 閰嶇疆鍙傛暟绠＄悊          鈹�    鈥� 寮傛浜嬩欢闃熷垪         鈹�    鈥� 璐熻浇鍧囪　澶勭悊           鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
                                        鈫曪笍 绯荤粺鎶借薄鎺ュ彛
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹�                        馃煛 绯荤粺鎶借薄灞� (Abstraction Layer)                        鈹�
鈹溾攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹�    QIO Framework        鈹�  OS Abstraction Layer    鈹�     Network QIO            鈹�
鈹�  鈥� 闃熷垪I/O鎶借薄          鈹�    鈥� 璺ㄥ钩鍙板吋瀹瑰眰         鈹�    鈥� 缃戠粶閫氫俊鎶借薄           鈹�
鈹�  鈥� 缁熶竴I/O鎺ュ彛          鈹�    鈥� 绯荤粺璋冪敤灏佽         鈹�    鈥� 鍗忚鏍堟娊璞�             鈹�
鈹�  鈥� 寮傛I/O鏀寔          鈹�    鈥� 绾跨▼鍚屾鍘熻         鈹�    鈥� 杩炴帴姹犵鐞�             鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
                                        鈫曪笍 搴曞眰閫氫俊鏈哄埗
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹�                        馃敶 搴曞眰IPC鏈哄埗 (Low-level IPC)                           鈹�
鈹溾攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹� Shared Memory  鈹� Message Queue 鈹�   Socket    鈹�    Pipe     鈹� Semaphore 鈹� Mutex  鈹�
鈹� 鈥� 楂橀€熸暟鎹紶杈�  鈹� 鈥� 寮傛娑堟伅浼犻€� 鈹� 鈥� 缃戠粶閫氫俊   鈹� 鈥� 杩涚▼绠￠亾   鈹� 鈥� 淇″彿閲�   鈹� 鈥� 浜掓枼閿佲攤
鈹� 鈥� 鐜舰缂撳啿鍖�   鈹� 鈥� 浼樺厛绾ч槦鍒�   鈹� 鈥� TCP/UDP   鈹� 鈥� 鍛藉悕绠￠亾   鈹� 鈥� 璁℃暟鎺у埗 鈹� 鈥� 涓寸晫鍖衡攤
鈹� 鈥� 鍐呭瓨鏄犲皠     鈹� 鈥� 娑堟伅鎸佷箙鍖�   鈹� 鈥� Unix鍩熷鎺ュ瓧鈹� 鈥� 鍖垮悕绠￠亾  鈹� 鈥� 璧勬簮鎺у埗 鈹� 鈥� 姝婚攣閬垮厤鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
                                        鈫曪笍 澶栭儴绯荤粺鎺ュ彛
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹�                          鈿� 澶栭儴绯荤粺 (External Systems)                         鈹�
鈹溾攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹�    File System          鈹�   Printer Hardware       鈹�    Network Services        鈹�
鈹�  鈥� 鏂囦欢璇诲啓鎿嶄綔          鈹�    鈥� 鎵撳嵃寮曟搸鎺у埗         鈹�    鈥� 缃戠粶鏈嶅姟鍙戠幇           鈹�
鈹�  鈥� 鐩綍閬嶅巻绠＄悊          鈹�    鈥� 鎵弿浠帶鍒�           鈹�    鈥� 杩滅▼鏈嶅姟璋冪敤           鈹�
鈹�  鈥� 鏉冮檺鎺у埗绠＄悊          鈹�    鈥� 纭欢鐘舵€佺洃鎺�         鈹�    鈥� 鏈嶅姟娉ㄥ唽鍙戝竷           鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�

鏋舵瀯灞傛璇︾粏鑱岃矗琛�:
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹�     鏋舵瀯灞傛     鈹�                        涓昏鑱岃矗涓庣壒鐐�                        鈹�
鈹溾攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹尖攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹� 馃數 搴旂敤灞�        鈹� JavaScript搴旂敤鎵ц鐜锛岀敤鎴风晫闈氦浜掞紝涓氬姟閫昏緫瀹炵幇            鈹�
鈹� 馃煟 杩愯鏃跺眰      鈹� JavaScript寮曟搸绠＄悊锛孋-JS妗ユ帴閫氫俊锛屼簨浠跺惊鐜鐞�               鈹�
鈹� 馃煝 绠＄悊灞�        鈹� 绯荤粺璧勬簮鍗忚皟绠＄悊锛屾秷鎭矾鐢卞垎鍙戯紝妯″潡娉ㄥ唽鍙戠幇                 鈹�
鈹� 馃煛 鎶借薄灞�        鈹� 璺ㄥ钩鍙板吋瀹规娊璞★紝缁熶竴I/O鎺ュ彛锛岀綉缁滈€氫俊鎶借薄                   鈹�
鈹� 馃敶 IPC灞�         鈹� 搴曞眰杩涚▼闂撮€氫俊锛屽唴瀛樺叡浜満鍒讹紝鍚屾鍘熻瀹炵幇                   鈹�
鈹� 鈿� 澶栭儴绯荤粺      鈹� 鏂囦欢绯荤粺鎿嶄綔锛岀‖浠惰澶囨帶鍒讹紝缃戠粶鏈嶅姟闆嗘垚                     鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹粹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
```

**鏋舵瀯璇存槑**:
- **搴旂敤灞�**: 鎻愪緵鐢ㄦ埛浜や簰鐣岄潰鍜孞avaScript搴旂敤杩愯鐜
- **杩愯鏃跺眰**: 鏍稿績鐨凧avaScript寮曟搸鍜屾秷鎭ˉ鎺ユ満鍒�
- **绠＄悊灞�**: 璐熻矗绯荤粺璧勬簮绠＄悊鍜屾秷鎭矾鐢�
- **鎶借薄灞�**: 鎻愪緵璺ㄥ钩鍙扮殑I/O鍜屾搷浣滅郴缁熸娊璞�
- **搴曞眰IPC**: 瀹炵幇鍏蜂綋鐨勮繘绋嬮棿閫氫俊鏈哄埗

## 2. 鏍稿績IPC鏈哄埗鍒嗘瀽

### 2.1 Bridge閫氫俊鏈哄埗

Bridge妯″潡鏄疛avaScript涓嶤浠ｇ爜涔嬮棿鐨勫叧閿ˉ姊侊細

**鏍稿績鍔熻兘**:
- **C鈫扟S閫氫俊**: `send_to_bridge()` 鍑芥暟灏咰鏁版嵁浼犻€掔粰JavaScript
- **JS鈫扖閫氫俊**: `js_send_msg()` 鍑芥暟澶勭悊JavaScript鍙戦€佺殑娑堟伅
- **鏁版嵁灏佽**: 浣跨敤 `JS_NewArrayBufferCopy()` 杩涜鏁版嵁浼犻€�
- **寮傛璋冪敤**: 閫氳繃 `JS_Call()` 璋冪敤JavaScript鍥炶皟鍑芥暟

**鍏抽敭瀹炵幇**:
```c
void send_to_bridge(PeSFRunTime* prt, uint16_t data_length, const char* msg)
{
    JSValue value = JS_NewArrayBufferCopy(prt->qjs_ctx, (const unsigned char*)msg, data_length);
    JSValue on_msg = JS_GetPropertyStr(prt->qjs_ctx, bridge, "on_msg");
    JS_Call(prt->qjs_ctx, on_msg, bridge, 1, &value);
}
```

### 2.2 娑堟伅璺敱绯荤粺

娑堟伅璺敱绯荤粺璐熻矗妯″潡闂寸殑娑堟伅鍒嗗彂锛�

**璁捐鐗圭偣**:
- **妯″潡鍖栬璁�**: 姣忎釜妯″潡閫氳繃 `module_id` 鏍囪瘑
- **娑堟伅闃熷垪姹�**: `s_mail_array[]` 绠＄悊鎵€鏈夋ā鍧楃殑娑堟伅闃熷垪
- **绾跨▼瀹夊叏**: 浣跨敤 `pthread_mutex_lock` 淇濇姢璺敱琛�
- **娑堟伅缁撴瀯**: `ROUTER_MSG_S` 鍖呭惈娑堟伅绫诲瀷銆佸彂閫佽€呫€佹暟鎹瓑

**璺敱娴佺▼**:
1. 鏍规嵁 `module_id` 鎵惧埌瀵瑰簲鐨勬秷鎭槦鍒�
2. 灏� `ROUTER_MSG_S` 杞崲涓� `MSG_S`
3. 閫氳繃 `pi_msg_send` 鍙戦€佸埌鐩爣闃熷垪

### 2.3 鍏变韩鍐呭瓨QIO绯荤粺

QIO (Queue I/O) 鎻愪緵楂樻€ц兘鐨勮繘绋嬮棿閫氫俊锛�

**鏍稿績鏁版嵁缁撴瀯**:
```c
typedef struct shmbuf {
    sem_t sem_read;      // 璇诲悓姝ヤ俊鍙烽噺
    sem_t sem_write;     // 鍐欏悓姝ヤ俊鍙烽噺  
    sem_t sem_mutex;     // 浜掓枼淇″彿閲�
    uint32_t capacity;   // 鎬诲閲�
    uint32_t size;       // 褰撳墠鏁版嵁澶у皬
    uint32_t in, out;    // 鐜舰缂撳啿鍖烘寚閽�
    uint32_t flg_fin:1;  // 缁撴潫鏍囧織
} SHMBUF_S;
```

**鍚屾鏈哄埗**:
- **涓夐噸淇″彿閲�**: 璇汇€佸啓銆佷簰鏂ュ垎绂绘帶鍒�
- **鐜舰缂撳啿鍖�**: 楂樻晥鐨勫唴瀛樺埄鐢�
- **鐢熶骇鑰�-娑堣垂鑰呮ā寮�**: 鏀寔鍗曠敓浜ц€呭崟娑堣垂鑰�

## 3. 娑堟伅鍗忚涓庢暟鎹牸寮�

### 3.1 娑堟伅鍗忚鏋舵瀯鍥�

```
娑堟伅鍗忚灞傛缁撴瀯 (涓夊眰鍗忚鏍�)
鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺�

鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹�                          馃摝 Protocol Packet (鍗忚鍖呭眰)                         鈹�
鈹�                        [cmd + len_high + len_low + data]                       鈹�
鈹溾攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹� 鈥� cmd: 鍛戒护绫诲瀷 (1瀛楄妭)           鈥� len_high: 鏁版嵁闀垮害楂樺瓧鑺� (1瀛楄妭)              鈹�
鈹� 鈥� len_low: 鏁版嵁闀垮害浣庡瓧鑺� (1瀛楄妭)  鈥� data: 瀹為檯鏁版嵁杞借嵎 (鍙橀暱)                   鈹�
鈹� 鈥� 鍔熻兘: 搴曞眰浼犺緭鍗忚灏佽锛屾彁渚涚粺涓€鐨勬暟鎹寘鏍煎紡                                    鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
                                        鈫曪笍 鍗忚灏佽/瑙ｅ皝瑁�
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹�                        馃摠 Socket Message (濂楁帴瀛楁秷鎭眰)                         鈹�
鈹�                   [rtid + main + sub + respond + size + data]                  鈹�
鈹溾攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹� 鈥� rtid: 杩愯鏃禝D (2瀛楄妭)          鈥� main: 涓绘秷鎭被鍨� (2瀛楄妭)                     鈹�
鈹� 鈥� sub: 瀛愭秷鎭被鍨� (2瀛楄妭)         鈥� respond: 鍝嶅簲鏍囧織 (2瀛楄妭)                    鈹�
鈹� 鈥� size: 鏁版嵁澶у皬 (4瀛楄妭)          鈥� data: 涓氬姟鏁版嵁 (鍙橀暱)                       鈹�
鈹� 鈥� 鍔熻兘: 搴旂敤灞傛秷鎭牸寮忥紝鏀寔澶氬簲鐢ㄥ疄渚嬪拰娑堟伅鍒嗙被                                  鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
                                        鈫曪笍 娑堟伅璺敱/鍒嗗彂
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹�                         馃幆 Router Message (璺敱娑堟伅灞�)                          鈹�
鈹�                            [module_id + msg_data]                             鈹�
鈹溾攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹� 鈥� module_id: 鐩爣妯″潡鏍囪瘑绗�        鈥� msg_data: 妯″潡鐗瑰畾鏁版嵁                      鈹�
鈹� 鈥� 鍔熻兘: 鍐呴儴娑堟伅璺敱锛屽皢娑堟伅鍒嗗彂鍒板叿浣撶殑涓氬姟澶勭悊妯″潡                              鈹�
鈹� 鈥� 鐗圭偣: 鏀寔鍔ㄦ€佹ā鍧楁敞鍐屽拰娑堟伅璁㈤槄鏈哄埗                                           鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�

娑堟伅绫诲瀷鍒嗙被璇︾粏琛�:
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹�                              娑堟伅绫诲瀷鍒嗙被浣撶郴                                    鈹�
鈹溾攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹�    MAIN_MSG         鈹�      SUB_MSG        鈹�         Protocol_CMD                鈹�
鈹�   (涓绘秷鎭被鍨�)       鈹�    (瀛愭秷鎭被鍨�)      鈹�        (鍗忚鍛戒护)                    鈹�
鈹溾攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹尖攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹尖攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹� MSG_MODULE_PANEL    鈹� MSG_PANEL_SUB_DRAW  鈹� CMD_PRINTER_TO_APP                  鈹�
鈹� 鈥� 闈㈡澘妯″潡娑堟伅       鈹� 鈥� 闈㈡澘缁樺埗瀛愭秷鎭�     鈹� 鈥� 鎵撳嵃鏈哄埌搴旂敤娑堟伅                   鈹�
鈹�                     鈹�                     鈹�                                     鈹�
鈹� MSG_MODULE_PRINT    鈹� MSG_PRINT_SUB_      鈹� CMD_APP_TO_PRINTER                  鈹�
鈹� 鈥� 鎵撳嵃妯″潡娑堟伅       鈹� JOB_STATE           鈹� 鈥� 搴旂敤鍒版墦鍗版満娑堟伅                   鈹�
鈹�                     鈹� 鈥� 鎵撳嵃浣滀笟鐘舵€�       鈹�                                     鈹�
鈹�                     鈹�                     鈹�                                     鈹�
鈹� MSG_MODULE_SCAN     鈹� MSG_SCAN_SUB_STATUS 鈹� CMD_APP_START                       鈹�
鈹� 鈥� 鎵弿妯″潡娑堟伅       鈹� 鈥� 鎵弿鐘舵€佸瓙娑堟伅     鈹� 鈥� 搴旂敤鍚姩鍛戒护                       鈹�
鈹�                     鈹�                     鈹�                                     鈹�
鈹� MSG_MODULE_NET      鈹� MSG_NET_SUB_CONFIG  鈹� CMD_APP_END                         鈹�
鈹� 鈥� 缃戠粶妯″潡娑堟伅       鈹� 鈥� 缃戠粶閰嶇疆瀛愭秷鎭�     鈹� 鈥� 搴旂敤缁撴潫鍛戒护                       鈹�
鈹�                     鈹�                     鈹�                                     鈹�
鈹� MSG_MODULE_SYSTEM   鈹� MSG_SYS_SUB_STATUS  鈹� CMD_PING_PACKET                     鈹�
鈹� 鈥� 绯荤粺妯″潡娑堟伅       鈹� 鈥� 绯荤粺鐘舵€佸瓙娑堟伅     鈹� 鈥� 蹇冭烦妫€娴嬪寘                         鈹�
鈹�                     鈹�                     鈹�                                     鈹�
鈹� MSG_MODULE_FILE     鈹� MSG_FILE_SUB_OP     鈹� CMD_PONG_PACKET                     鈹�
鈹� 鈥� 鏂囦欢妯″潡娑堟伅       鈹� 鈥� 鏂囦欢鎿嶄綔瀛愭秷鎭�     鈹� 鈥� 蹇冭烦鍝嶅簲鍖�                         鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹粹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹粹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�

娑堟伅娴佽浆鏈哄埗:
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹�                              娑堟伅澶勭悊娴佺▼                                        鈹�
鈹溾攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹� 1. 鍗忚鍖呮帴鏀� 鈫� 瑙ｆ瀽cmd瀛楁 鈫� 鎻愬彇鏁版嵁闀垮害 鈫� 璇诲彇瀹屾暣鏁版嵁                         鈹�
鈹� 2. 濂楁帴瀛楁秷鎭В鏋� 鈫� 鎻愬彇rtid 鈫� 纭畾鐩爣搴旂敤 鈫� 瑙ｆ瀽main/sub绫诲瀷                   鈹�
鈹� 3. 璺敱娑堟伅鍒嗗彂 鈫� 鏌ユ壘module_id 鈫� 璋冪敤娉ㄥ唽澶勭悊鍣� 鈫� 鎵ц涓氬姟閫昏緫                  鈹�
鈹� 4. 鍝嶅簲娑堟伅鐢熸垚 鈫� 璁剧疆respond鏍囧織 鈫� 鍘熻矾寰勮繑鍥� 鈫� 瀹屾垚娑堟伅寰幆                    鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
```

### 3.2 绾跨▼瀹夊叏鏈哄埗鍥�

```
绾跨▼瀹夊叏璧勬簮姹犳灦鏋� (涓夊眰淇濇姢鏈哄埗)
鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺�

鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹�                          馃強鈥嶁檪锔� 璧勬簮姹犵鐞� (Resource Pool Management)                鈹�
鈹溾攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹�    馃敀 浜掓枼閿佹睜         鈹�    馃殾 淇″彿閲忔睜         鈹�      馃У 绾跨▼姹�                  鈹�
鈹�  (Mutex Pool)         鈹�  (Semaphore Pool)     鈹�   (Thread Pool)               鈹�
鈹�                       鈹�                       鈹�                               鈹�
鈹� 鈥� pi_mutex_create()   鈹� 鈥� pi_sem_create()     鈹� 鈥� pi_thread_create()          鈹�
鈹� 鈥� pi_mutex_destroy()  鈹� 鈥� pi_sem_destroy()    鈹� 鈥� pi_thread_join()            鈹�
鈹� 鈥� pi_mutex_lock()     鈹� 鈥� pi_sem_wait()       鈹� 鈥� pi_thread_detach()          鈹�
鈹� 鈥� pi_mutex_unlock()   鈹� 鈥� pi_sem_post()       鈹� 鈥� 绾跨▼姹犲姩鎬佹墿缂╁              鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
                                        鈫曪笍 鍚屾鏈哄埗搴旂敤
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹�                            鈿欙笍 鍚屾鏈哄埗 (Synchronization)                        鈹�
鈹溾攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹�   馃搵 闃熷垪浜掓枼閿�        鈹�   馃敆 閾捐〃浜掓枼閿�        鈹�   馃捑 鍏变韩鍐呭瓨淇″彿閲�             鈹�
鈹�  (Queue Mutex)        鈹�  (List Mutex)         鈹�  (Shared Memory Semaphore)   鈹�
鈹�                       鈹�                       鈹�                               鈹�
鈹� 鈥� m_queue_mutex       鈹� 鈥� m_list_mutex        鈹� 鈥� sem_read/write/mutex        鈹�
鈹� 鈥� 淇濇姢娑堟伅闃熷垪鎿嶄綔     鈹� 鈥� 淇濇姢閾捐〃閬嶅巻鎿嶄綔     鈹� 鈥� 涓夐噸淇″彿閲忔満鍒�               鈹�
鈹� 鈥� 鍏ラ槦/鍑洪槦鍘熷瓙鎬�     鈹� 鈥� 鑺傜偣澧炲垹鍘熷瓙鎬�       鈹� 鈥� 璇诲啓鍒嗙鎺у埗                 鈹�
鈹� 鈥� 闃叉闃熷垪绔炴€佹潯浠�     鈹� 鈥� 闃叉閾捐〃缁撴瀯鐮村潖     鈹� 鈥� 鐢熶骇鑰�-娑堣垂鑰呮ā寮�            鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
                                        鈫曪笍 鎿嶄綔淇濇姢瀹炵幇
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹�                           馃洝锔� 鎿嶄綔淇濇姢 (Operation Protection)                    鈹�
鈹溾攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹�  馃摛馃摜 闃熷垪Push/Pop淇濇姢  鈹�  馃攳 閾捐〃閬嶅巻淇濇姢       鈹�  馃摉馃摑 鍏变韩鍐呭瓨璇诲啓淇濇姢          鈹�
鈹�                       鈹�                       鈹�                               鈹�
鈹� 鈥� 鍘熷瓙鎬у叆闃熷嚭闃熸搷浣�   鈹� 鈥� 闃叉骞跺彂淇敼鍐茬獊     鈹� 鈥� 鐜舰缂撳啿鍖哄悓姝�               鈹�
鈹� 鈥� 闃熷垪婊�/绌虹姸鎬佹鏌�   鈹� 鈥� 杩唬鍣ㄥけ鏁堜繚鎶�       鈹� 鈥� 璇诲啓鎸囬拡鍘熷瓙鏇存柊             鈹�
鈹� 鈥� 浼樺厛绾ч槦鍒楁敮鎸�       鈹� 鈥� 鑺傜偣鐢熷懡鍛ㄦ湡绠＄悊     鈹� 鈥� 缂撳啿鍖烘孩鍑烘娴�               鈹�
鈹� 鈥� 瓒呮椂绛夊緟鏈哄埗         鈹� 鈥� 鍙屽悜閾捐〃瀹屾暣鎬�       鈹� 鈥� 鍐呭瓨灞忛殰淇濊瘉                 鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�

鍏变韩鍐呭瓨鍚屾璇︾粏鏈哄埗:
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹�                      馃捑 SHMBUF_S (鍏变韩鍐呭瓨缂撳啿鍖虹粨鏋�)                           鈹�
鈹�                    [capacity + size + in/out + flg_fin]                       鈹�
鈹溾攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹� 鈥� capacity: 缂撳啿鍖烘€诲閲�    鈥� size: 褰撳墠鏁版嵁澶у皬                                 鈹�
鈹� 鈥� in: 鍐欏叆浣嶇疆鎸囬拡         鈥� out: 璇诲彇浣嶇疆鎸囬拡                                   鈹�
鈹� 鈥� flg_fin: 缁撴潫鏍囧織浣�      鈥� 鐜舰缂撳啿鍖哄疄鐜�                                      鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
                                鈫曪笍 涓夐噸淇″彿閲忓垎绂绘帶鍒�
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹�                            涓夐噸淇″彿閲忓崗璋冩満鍒�                                    鈹�
鈹溾攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹�   馃摉 sem_read       鈹�   馃摑 sem_write      鈹�      馃敀 sem_mutex                   鈹�
鈹�   (璇讳俊鍙烽噺)         鈹�   (鍐欎俊鍙烽噺)         鈹�     (浜掓枼淇″彿閲�)                     鈹�
鈹溾攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹尖攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹尖攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹� 鈥� 鎺у埗璇绘搷浣滄潈闄�     鈹� 鈥� 鎺у埗鍐欐搷浣滄潈闄�     鈹� 鈥� 浜掓枼璁块棶鎺у埗                       鈹�
鈹� 鈥� 鍙敤鏁版嵁璁℃暟       鈹� 鈥� 鍙敤绌洪棿璁℃暟       鈹� 鈥� 闃叉绔炴€佹潯浠�                       鈹�
鈹� 鈥� 璇昏€呴樆濉炵瓑寰�       鈹� 鈥� 鍐欒€呴樆濉炵瓑寰�       鈹� 鈥� 鍘熷瓙鎿嶄綔淇濊瘉                       鈹�
鈹� 鈥� 鐢熶骇鑰�-娑堣垂鑰呰鍙�  鈹� 鈥� 鐢熶骇鑰�-娑堣垂鑰呭啓鍏�  鈹� 鈥� 涓寸晫鍖轰繚鎶�                         鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹粹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹粹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�

鍚屾鎿嶄綔娴佺▼:
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹�                              璇诲啓鎿嶄綔鍚屾娴佺▼                                    鈹�
鈹溾攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹� 馃摉 璇绘搷浣滄祦绋�:                                                                   鈹�
鈹�   1. sem_wait(sem_read)    鈫� 绛夊緟鍙鏁版嵁                                       鈹�
鈹�   2. sem_wait(sem_mutex)   鈫� 鑾峰彇浜掓枼閿�                                         鈹�
鈹�   3. 璇诲彇鏁版嵁 + 鏇存柊out鎸囬拡 鈫� 鎵ц璇绘搷浣�                                         鈹�
鈹�   4. sem_post(sem_mutex)   鈫� 閲婃斁浜掓枼閿�                                         鈹�
鈹�   5. sem_post(sem_write)   鈫� 閲婃斁鍐欑┖闂�                                         鈹�
鈹�                                                                                 鈹�
鈹� 馃摑 鍐欐搷浣滄祦绋�:                                                                   鈹�
鈹�   1. sem_wait(sem_write)   鈫� 绛夊緟鍙啓绌洪棿                                       鈹�
鈹�   2. sem_wait(sem_mutex)   鈫� 鑾峰彇浜掓枼閿�                                         鈹�
鈹�   3. 鍐欏叆鏁版嵁 + 鏇存柊in鎸囬拡  鈫� 鎵ц鍐欐搷浣�                                         鈹�
鈹�   4. sem_post(sem_mutex)   鈫� 閲婃斁浜掓枼閿�                                         鈹�
鈹�   5. sem_post(sem_read)    鈫� 閫氱煡鍙鏁版嵁                                       鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
```

### 3.2 鍗忚灞傛缁撴瀯

**Protocol Packet (鍗忚鍖�)**:
```c
struct protocol_packet {
    uint8_t cmd;        // 鍛戒护绫诲瀷
    uint8_t len_high;   // 闀垮害楂樺瓧鑺�
    uint8_t len_low;    // 闀垮害浣庡瓧鑺�
    uint8_t data[0];    // 鏁版嵁
};
```

**Socket Message (濂楁帴瀛楁秷鎭�)**:
```c
struct socket_msg {
    uint8_t rtid;       // 杩愯鏃禝D
    MAIN_MSG_E main;    // 涓绘秷鎭被鍨�
    SUB_MSG_E sub;      // 瀛愭秷鎭被鍨�
    int32_t respond;    // 鍝嶅簲鐮�
    int32_t size;       // 鏁版嵁闀垮害
    uint8_t data[0];    // 鏁版嵁
};
```

### 3.3 娑堟伅绫诲瀷鏋氫妇

**涓绘秷鎭被鍨� (MAIN_MSG)**:
- `MSG_MODULE_PANEL`: 闈㈡澘妯″潡
- `MSG_MODULE_PRINT`: 鎵撳嵃妯″潡
- `MSG_MODULE_SCAN`: 鎵弿妯″潡
- `MSG_MODULE_NET`: 缃戠粶妯″潡
- 绛夌瓑...

**鍗忚鍛戒护 (Protocol_CMD)**:
- `CMD_PRINTER_TO_APP`: 鎵撳嵃鏈哄埌搴旂敤
- `CMD_APP_TO_PRINTER`: 搴旂敤鍒版墦鍗版満
- `CMD_APP_START`: 搴旂敤鍚姩
- `CMD_APP_END`: 搴旂敤缁撴潫

## 4. 绾跨▼瀹夊叏涓庡悓姝ユ満鍒�

### 4.1 璧勬簮姹犵鐞�

**浜掓枼閿佹睜**:
- 棰勫垎閰嶅浐瀹氭暟閲忕殑浜掓枼閿�
- 閫氳繃 `pi_mutex_create()` 鑾峰彇鍙敤閿�
- 鏀寔瓒呮椂閿佸畾 `pi_mutex_timedlock()`

**淇″彿閲忔睜**:
- 棰勫垎閰嶅浐瀹氭暟閲忕殑淇″彿閲�
- 鏀寔璁℃暟淇″彿閲忓拰浜岃繘鍒朵俊鍙烽噺
- 鎻愪緵瓒呮椂绛夊緟鏈哄埗

**绾跨▼姹�**:
- 缁熶竴绠＄悊绯荤粺绾跨▼
- 鏀寔绾跨▼浼樺厛绾ц缃�
- 鎻愪緵绾跨▼鐘舵€佺洃鎺�

### 4.2 鍚屾绛栫暐

**娑堟伅闃熷垪鍚屾**:
- `m_queue_mutex`: 淇濇姢娑堟伅闃熷垪鐨刾ush/pop鎿嶄綔
- 澶存彃娉曞拰灏炬彃娉曠殑绾跨▼瀹夊叏瀹炵幇

**閾捐〃鍚屾**:
- `m_list_mutex`: 淇濇姢娑堟伅閾捐〃鐨勯亶鍘嗗拰淇敼
- 闃叉骞跺彂璁块棶瀵艰嚧鐨勬暟鎹珵浜�

**瀹㈡埛绔啓鍚屾**:
- `clientWriteFlag`: 鎺у埗瀹㈡埛绔啓鎿嶄綔鐨勪簰鏂�
- 蹇欑瓑寰呮満鍒剁‘淇濆啓鎿嶄綔鐨勫師瀛愭€�

### 4.3 鍏变韩鍐呭瓨鍚屾

**涓夐噸淇″彿閲忔満鍒�**:
- `sem_read`: 鎺у埗璇绘搷浣滅殑鍚屾
- `sem_write`: 鎺у埗鍐欐搷浣滅殑鍚屾
- `sem_mutex`: 鎻愪緵浜掓枼璁块棶淇濇姢

**鐜舰缂撳啿鍖虹鐞�**:
- `in/out` 鎸囬拡鐨勫師瀛愭€ф洿鏂�
- 缂撳啿鍖烘弧/绌虹姸鎬佺殑姝ｇ‘澶勭悊
- 鐢熶骇鑰�-娑堣垂鑰呮ā寮忕殑姝婚攣棰勯槻

## 5. 鏁版嵁娴佸悜鍒嗘瀽

### 5.1 鏁版嵁娴佸悜鍥�

```
PEDK IPC 鏁版嵁娴佸悜鍒嗘瀽 (涓夊ぇ娴佺▼寰幆)
鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺�

馃搳 鏁版嵁娴佸悜姒傝 - 瀹屾暣鐨勭敤鎴蜂氦浜掑埌纭欢鎵ц鐨勯棴鐜祦绋�

馃攧 娴佺▼1: 鐢ㄦ埛浜や簰娴� (User Interaction Flow)
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹� 馃懁 鐢ㄦ埛 鈹€鈹€鉃★笍 鐢ㄦ埛鎿嶄綔 鈹€鈹€鉃★笍 馃枼锔� Panel UI 鈹€鈹€鉃★笍 闈㈡澘浜嬩欢 鈹€鈹€鉃★笍 鈿� Event Manager      鈹�
鈹�                                                                                 鈹�
鈹�                                    猬囷笍 浜嬩欢娑堟伅                                   鈹�
鈹�                                                                                 鈹�
鈹� 馃摫 JavaScript App 猬咃笍 璋冪敤JS鍥炶皟 猬咃笍 馃寜 Bridge Module 猬咃笍 鍒嗗彂鍒板簲鐢� 猬咃笍 鈿欙笍 PeSF Runtime 鈹�
鈹�                    猬嗭笍                                    猬嗭笍                      鈹�
鈹�                灏佽娑堟伅                              馃敡 PEDK Manager              鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�

璇︾粏姝ラ璇存槑:
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹�     缁勪欢        鈹�                        澶勭悊鍐呭                             鈹�
鈹溾攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹尖攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹� 馃懁 鐢ㄦ埛          鈹� 鎸夐敭鎿嶄綔銆佽Е鎽歌緭鍏ャ€佽闊虫寚浠ょ瓑鐗╃悊浜や簰                       鈹�
鈹� 馃枼锔� Panel UI     鈹� 鎹曡幏鐢ㄦ埛杈撳叆銆佺晫闈簨浠跺鐞嗐€佽緭鍏ラ獙璇�                         鈹�
鈹� 鈿� Event Manager 鈹� 浜嬩欢鍒嗙被銆佷紭鍏堢骇澶勭悊銆佷簨浠堕槦鍒楃鐞�                           鈹�
鈹� 馃敡 PEDK Manager  鈹� 娑堟伅灏佽銆佸崗璁浆鎹€€佺洰鏍囧簲鐢ㄨ矾鐢�                             鈹�
鈹� 鈿欙笍 PeSF Runtime  鈹� 搴旂敤瀹炰緥绠＄悊銆佹秷鎭垎鍙戙€佺敓鍛藉懆鏈熸帶鍒�                         鈹�
鈹� 馃寜 Bridge Module 鈹� JS-C妗ユ帴銆佹暟鎹被鍨嬭浆鎹€€佸紓姝ュ洖璋冨鐞�                        鈹�
鈹� 馃摫 JavaScript App鈹� 涓氬姟閫昏緫澶勭悊銆佹暟鎹獙璇併€佸搷搴旂敓鎴�                             鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹粹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
```

馃攧 娴佺▼2: 搴旂敤鍝嶅簲娴� (Application Response Flow)
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹� 馃摫 JavaScript App 鈹€鈹€鉃★笍 涓氬姟閫昏緫澶勭悊 鈹€鈹€鉃★笍 馃寜 Bridge Module 鈹€鈹€鉃★笍 JS娑堟伅 鈹€鈹€鉃★笍 鈿欙笍 PeSF Runtime 鈹�
鈹�                                                                                 鈹�
鈹�                                  猬囷笍 璺敱鍒扮洰鏍囨ā鍧�                               鈹�
鈹�                                                                                 鈹�
鈹� 鉁� 纭欢鍝嶅簲 猬咃笍 鎵ц鎿嶄綔 猬咃笍 锟斤笍 鎵撳嵃鏈虹‖浠� 猬咃笍 鍙戦€佸埌纭欢 猬咃笍 馃敡 PEDK Manager        鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�

璇︾粏姝ラ璇存槑:
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹�     缁勪欢        鈹�                        澶勭悊鍐呭                             鈹�
鈹溾攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹尖攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹� 馃摫 JavaScript App鈹� 涓氬姟閫昏緫鎵ц銆佹暟鎹鐞嗐€佺畻娉曡绠�                             鈹�
鈹� 馃寜 Bridge Module 鈹� JS-C鏁版嵁杞崲銆佺被鍨嬫槧灏勩€佸唴瀛樼鐞�                            鈹�
鈹� 鈿欙笍 PeSF Runtime  鈹� 娑堟伅璺敱銆佹ā鍧楄皟搴︺€佽祫婧愬垎閰�                                 鈹�
鈹� 馃敡 PEDK Manager  鈹� 纭欢鎺ュ彛璋冪敤銆佸懡浠ゅ皝瑁呫€佸崗璁浆鎹�                             鈹�
鈹� 馃枿锔� 鎵撳嵃鏈虹‖浠�    鈹� 鐗╃悊鎿嶄綔鎵ц銆佺姸鎬佺洃鎺с€侀敊璇娴�                             鈹�
鈹� 鉁� 纭欢鍝嶅簲      鈹� 鎵ц缁撴灉銆佺姸鎬佷俊鎭€侀敊璇姤鍛�                                 鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹粹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�

馃攧 娴佺▼3: 鐘舵€佸弽棣堟祦 (Status Feedback Flow)
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹� 馃枿锔� 鎵撳嵃鏈虹‖浠� 鈹€鈹€鉃★笍 鐘舵€佸彉鍖� 鈹€鈹€鉃★笍 馃敡 PEDK Manager 鈹€鈹€鉃★笍 鐘舵€佹洿鏂� 鈹€鈹€鉃★笍 鈿欙笍 PeSF Runtime 鈹�
鈹�                                                                                 鈹�
鈹�                                  猬囷笍 鍒嗗彂鐘舵€佹秷鎭�                                 鈹�
鈹�                                                                                 鈹�
鈹� 馃懁 鐢ㄦ埛 猬咃笍 鐣岄潰鏇存柊 猬咃笍 馃枼锔� Panel UI 猬咃笍 鏇存柊UI鏄剧ず 猬咃笍 馃寜 Bridge Module           鈹�
鈹�                                                                                 鈹�
鈹�                                  猬嗭笍 閫氱煡搴旂敤                                     鈹�
鈹�                                                                                 鈹�
鈹�                              馃摫 JavaScript App                                 鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�

璇︾粏姝ラ璇存槑:
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹�     缁勪欢        鈹�                        澶勭悊鍐呭                             鈹�
鈹溾攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹尖攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹� 馃枿锔� 鎵撳嵃鏈虹‖浠�    鈹� 鐘舵€佸彉鍖栫洃鎺с€佷簨浠惰Е鍙戙€佸紓甯告娴�                             鈹�
鈹� 馃敡 PEDK Manager  鈹� 鐘舵€佷俊鎭敹闆嗐€佹秷鎭牸寮忓寲銆佷簨浠跺垎绫�                           鈹�
鈹� 鈿欙笍 PeSF Runtime  鈹� 鐘舵€佹秷鎭垎鍙戙€佸簲鐢ㄩ€氱煡銆乁I鏇存柊瑙﹀彂                          鈹�
鈹� 馃寜 Bridge Module 鈹� 鐘舵€佹暟鎹浆鎹€€丣avaScript鍥炶皟銆佸紓姝ラ€氱煡                      鈹�
鈹� 馃摫 JavaScript App鈹� 鐘舵€佸鐞嗛€昏緫銆乁I鏁版嵁鏇存柊銆佺敤鎴烽€氱煡                          鈹�
鈹� 馃枼锔� Panel UI     鈹� 鐣岄潰鐘舵€佹洿鏂般€佽瑙夊弽棣堛€佺敤鎴锋彁绀�                             鈹�
鈹� 馃懁 鐢ㄦ埛          鈹� 鐘舵€佹劅鐭ャ€佹搷浣滃弽棣堛€佸喅绛栧埗瀹�                                 鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹粹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�

馃攧 瀹屾暣寰幆娴佺▼ (Complete Cycle Flow)
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹�                              绔埌绔暟鎹祦杞摼璺�                                  鈹�
鈹溾攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹� 馃懁 鐢ㄦ埛鎿嶄綔 鈫� 馃枼锔� 闈㈡澘浜嬩欢 鈫� 鈿� 浜嬩欢娑堟伅 鈫� 馃摝 娑堟伅灏佽 鈫� 馃殌 搴旂敤鍒嗗彂 鈫� 鈿� JS澶勭悊    鈹�
鈹�      鈫�                                                                          鈹�
鈹� 馃憗锔� 鐢ㄦ埛鎰熺煡 鈫� 锟斤笍 UI鏇存柊 鈫� 馃摫 搴旂敤閫氱煡 鈫� 馃摗 娑堟伅鍒嗗彂 鈫� 馃搳 鐘舵€佸弽棣� 鈫� 馃枿锔� 纭欢鎵ц  鈹�
鈹�      鈫�                                                                          鈹�
鈹� 锟� 娑堟伅璺敱 鈫� 馃枿锔� 纭欢鎵ц 鈫� 馃搳 鐘舵€佸弽棣� 鈫� 馃摗 娑堟伅鍒嗗彂 鈫� 馃摫 搴旂敤閫氱煡 鈫� 馃枼锔� UI鏇存柊   鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�

娴佺▼鐗圭偣鍒嗘瀽:
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹�                              鍏抽敭娴佺▼鐗瑰緛                                        鈹�
鈹溾攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹� 馃攧 寮傛澶勭悊: 鎵€鏈夋祦绋嬮兘鏀寔寮傛鎵ц锛岄伩鍏嶉樆濉炵敤鎴风晫闈�                             鈹�
鈹� 锟斤笍 閿欒澶勭悊: 姣忎釜鐜妭閮芥湁閿欒妫€娴嬪拰鎭㈠鏈哄埗                                      鈹�
鈹� 馃搳 鐘舵€佺鐞�: 瀹炴椂鐘舵€佸悓姝ワ紝纭繚绯荤粺鐘舵€佷竴鑷存€�                                     鈹�
鈹� 鈿� 鎬ц兘浼樺寲: 娑堟伅闃熷垪銆佺紦瀛樻満鍒躲€佹壒澶勭悊浼樺寲                                       鈹�
鈹� 锟� 绾跨▼瀹夊叏: 澶氱嚎绋嬬幆澧冧笅鐨勬暟鎹悓姝ュ拰绔炴€佹潯浠堕槻鎶�                                 鈹�
鈹� 馃搱 鍙墿灞曟€�: 妯″潡鍖栬璁℃敮鎸佸姛鑳芥墿灞曞拰绯荤粺鍗囩骇                                     鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
```

### 5.2 鐢ㄦ埛浜や簰娴�
1. Panel UI 鎺ユ敹鐢ㄦ埛杈撳叆
2. Event Manager 澶勭悊闈㈡澘浜嬩欢
3. PEDK Manager 灏佽娑堟伅
4. PeSF Runtime 鍒嗗彂鍒板搴斿簲鐢�
5. Bridge Module 璋冪敤JavaScript鍥炶皟

### 5.3 搴旂敤鍝嶅簲娴�
1. JavaScript App 澶勭悊涓氬姟閫昏緫
2. Bridge Module 鎺ユ敹JS娑堟伅
3. PeSF Runtime 璺敱鍒扮洰鏍囨ā鍧�
4. PEDK Manager 鍙戦€佸埌鎵撳嵃鏈虹‖浠�
5. 纭欢鎵ц鐩稿簲鎿嶄綔

### 5.4 鐘舵€佸弽棣堟祦
1. 鎵撳嵃鏈虹‖浠剁姸鎬佸彉鍖�
2. PEDK Manager 鎺ユ敹鐘舵€佹洿鏂�
3. PeSF Runtime 鍒嗗彂鐘舵€佹秷鎭�
4. Bridge Module 閫氱煡JavaScript App
5. JavaScript App 鏇存柊UI鏄剧ず

## 6. 鎬ц兘涓庡彲闈犳€ц瘎浼�

### 6.1 浼樺娍
- **鍒嗗眰璁捐**: 娓呮櫚鐨勬灦鏋勫眰娆★紝渚夸簬缁存姢鍜屾墿灞�
- **寮傛澶勭悊**: 閬垮厤闃诲锛屾彁楂樼郴缁熷搷搴旀€�
- **鍐呭瓨鍏变韩**: 楂樻晥鐨勫ぇ鏁版嵁浼犺緭鏈哄埗
- **妯″潡鍖�**: 鏉捐€﹀悎璁捐锛屾槗浜庡姛鑳芥墿灞�
- **绾跨▼瀹夊叏**: 瀹屽杽鐨勫悓姝ユ満鍒朵繚璇佹暟鎹竴鑷存€�

### 6.2 娼滃湪闂
- **鍐呭瓨娉勬紡椋庨櫓**: JavaScript瀵硅薄闇€瑕佹纭噴鏀惧紩鐢ㄨ鏁�
- **姝婚攣鍙兘**: 澶氶噸閿佺殑浣跨敤闇€瑕佽皑鎱庣殑閿侀『搴忕鐞�
- **鎬ц兘鐡堕**: 娑堟伅搴忓垪鍖�/鍙嶅簭鍒楀寲鍙兘鎴愪负鎬ц兘鐡堕
- **閿欒浼犳挱**: 璺ㄥ眰娆＄殑閿欒澶勭悊鏈哄埗闇€瑕佸畬鍠�
- **璧勬簮绔炰簤**: 楂樺苟鍙戝満鏅笅鐨勮祫婧愭睜绠＄悊闇€瑕佷紭鍖�

## 7. 浼樺寲寤鸿

### 7.1 鎬ц兘浼樺寲
- 瀹炵幇娑堟伅姹犳満鍒讹紝鍑忓皯鍐呭瓨鍒嗛厤/閲婃斁寮€閿€
- 浼樺寲娑堟伅搴忓垪鍖栨牸寮忥紝鍑忓皯鏁版嵁鎷疯礉
- 寮曞叆寮傛I/O鏈哄埗锛屾彁楂樺苟鍙戝鐞嗚兘鍔�
- 瀹炵幇鏅鸿兘璺敱绠楁硶锛屽噺灏戞秷鎭浆鍙戝欢杩�

### 7.2 鍙潬鎬у寮�
- 瀹屽杽閿欒澶勭悊鍜屾仮澶嶆満鍒�
- 瀹炵幇娑堟伅閲嶄紶鍜岀‘璁ゆ満鍒�
- 娣诲姞绯荤粺鐩戞帶鍜岃瘖鏂姛鑳�
- 寮曞叆鐔旀柇鍣ㄦā寮忛槻姝㈢骇鑱旀晠闅�

### 7.3 鍙淮鎶ゆ€ф彁鍗�
- 缁熶竴鏃ュ織鏍煎紡鍜岀骇鍒鐞�
- 瀹屽杽API鏂囨。鍜屼娇鐢ㄧず渚�
- 瀹炵幇鑷姩鍖栨祴璇曟鏋�
- 鎻愪緵鎬ц兘鍒嗘瀽鍜岃皟璇曞伐鍏�

## 8. 鍏抽敭浠ｇ爜瀹炵幇鍒嗘瀽

### 8.1 娑堟伅灏佽涓庤В鏋�

**鍗忚灏佽鍑芥暟**:
```c
static struct protocol_packet* protocol_packing(uint8_t cmd, uint8_t* buf, uint16_t bufsize)
{
    struct protocol_packet* packet = pi_zalloc(sizeof(struct protocol_packet) + bufsize);
    packet->cmd = cmd;
    packet->len_high = (uint8_t)((bufsize & 0xFF00) >> 8);
    packet->len_low = (uint8_t)(bufsize & 0xFF);
    if (bufsize > 0 && buf != NULL) {
        memcpy(packet->data, buf, bufsize);
    }
    return packet;
}
```

**娑堟伅灏佽鍑芥暟**:
```c
static struct socket_msg* message_packing(MAIN_MSG_E main, SUB_MSG_E sub,
                                         int32_t respond, uint8_t* buf, int32_t bufsize)
{
    struct socket_msg* msg = malloc(sizeof(struct socket_msg) + bufsize);
    msg->rtid = 0x00;
    msg->main = main;
    msg->sub = sub;
    msg->respond = respond;
    msg->size = bufsize;
    if (bufsize > 0 && buf != NULL) {
        memcpy(msg->data, buf, bufsize);
    }
    return msg;
}
```

### 8.2 浜嬩欢鍒嗗彂鏈哄埗

**浜嬩欢鍒嗗彂鍒癑avaScript**:
```c
int dispatchEvent(MAIN_MSG main, SUB_MSG sub, int respond,
                  unsigned char *recvBuf, int recvBufSize)
{
    if(g_ctx == NULL) return -1;

    JSValueConst argv[] = {
        JS_NewInt32(g_ctx, main),
        JS_NewInt32(g_ctx, sub),
        JS_NewInt32(g_ctx, respond),
        JS_NewInt32(g_ctx, recvBufSize),
        JS_NewString(g_ctx, recvBuf)
    };
    send_to_bridge(g_ctx, sizeof(argv)/sizeof(argv[0]), argv);
    return 0;
}
```

### 8.3 鍚屾娑堟伅澶勭悊

**鍚屾娑堟伅鏁扮粍**:
```c
static syncMsg syncArr[] = {
    { MSG_MODULE_PANEL, MSG_PANEL_SUB_GET_BACK_TIMEOUT },
    { MSG_MODULE_PRINT, MSG_PRINT_SUB_JOB_STATE },
    { MSG_MODULE_PRINT, MSG_PRINT_SUB_JOB_ID },
    { MSG_MODULE_PRINT, MSG_PRINT_SUB_JOB_START },
};
```

**鍚屾娑堟伅妫€娴�**:
```c
int isSync = 0;
if(recvStruct.cmd == CMD_PRINTER_TO_APP) {
    struct strSocketMsg *recvInfo = (struct strSocketMsg *)package->data;
    int arrSize = sizeof(syncArr) / sizeof(syncArr[0]);
    for(int i = 0; i < arrSize; i++) {
        if(recvInfo->main == syncArr[i].main && recvInfo->sub == syncArr[i].sub) {
            isSync = 1;
            break;
        }
    }
}
```

## 9. 绯荤粺鐩戞帶涓庤皟璇�

### 9.1 鏃ュ織绯荤粺
- **鍒嗙骇鏃ュ織**: DEBUG銆両NFO銆乄ARN銆丒RROR鍥涗釜绾у埆
- **妯″潡鏍囪瘑**: 姣忎釜妯″潡閮芥湁鐙珛鐨勬棩蹇楁爣璇�
- **璋冪敤璺熻釜**: 閫氳繃 `__func__` 瀹忚褰曞嚱鏁拌皟鐢ㄩ摼

### 9.2 鎬ц兘鐩戞帶
- **娑堟伅闃熷垪闀垮害**: 鐩戞帶闃熷垪绉帇鎯呭喌
- **绾跨▼姹犱娇鐢ㄧ巼**: 璺熻釜绾跨▼璧勬簮浣跨敤
- **鍐呭瓨浣跨敤鎯呭喌**: 鐩戞帶鍏变韩鍐呭瓨鍜屽爢鍐呭瓨浣跨敤

### 9.3 閿欒澶勭悊
- **閿欒鐮佸畾涔�**: 缁熶竴鐨勯敊璇爜浣撶郴
- **寮傚父鎭㈠**: 鑷姩閲嶈瘯鍜岄檷绾ф満鍒�
- **璧勬簮娓呯悊**: 纭繚寮傚父鎯呭喌涓嬬殑璧勬簮姝ｇ‘閲婃斁

## 10. 鎵╁睍鎬ц璁�

### 10.1 妯″潡娉ㄥ唽鏈哄埗
```c
static ModuleContext bridge = {
    .module_name = "bridge",
    .module_init = bridge_init,
    .module_release = bridge_release,
    .module_instance = bridge_instance,
    .module_generalization = bridge_generalization
};
MODULE_REGISTER(bridge);
```

### 10.2 鍔ㄦ€佸姞杞芥敮鎸�
- **鎻掍欢鏋舵瀯**: 鏀寔杩愯鏃跺姞杞芥柊妯″潡
- **鐗堟湰鍏煎**: 鍚戝悗鍏煎鐨凙PI璁捐
- **閰嶇疆绠＄悊**: 鍔ㄦ€侀厤缃洿鏂版満鍒�

## 11. 瀹夊叏鑰冭檻

### 11.1 鍐呭瓨瀹夊叏
- **杈圭晫妫€鏌�**: 鎵€鏈夊唴瀛樿闂兘杩涜杈圭晫妫€鏌�
- **缂撳啿鍖烘孩鍑洪槻鎶�**: 浣跨敤瀹夊叏鐨勫瓧绗︿覆鎿嶄綔鍑芥暟
- **鍐呭瓨娉勬紡妫€娴�**: 瀹氭湡妫€鏌ュ唴瀛樺垎閰嶅拰閲婃斁

### 11.2 骞跺彂瀹夊叏
- **姝婚攣妫€娴�**: 瀹炵幇姝婚攣妫€娴嬪拰棰勯槻鏈哄埗
- **鍘熷瓙鎿嶄綔**: 鍏抽敭鏁版嵁浣跨敤鍘熷瓙鎿嶄綔淇濇姢
- **閿侀『搴�**: 缁熶竴鐨勯攣鑾峰彇椤哄簭閬垮厤姝婚攣

## 12. MFP鍥轰欢绔笌PEDK JS绔€氫俊鏈哄埗璇︾粏鍒嗘瀽

### 12.1 閫氫俊鏋舵瀯姒傝

MFP鍥轰欢绔笌PEDK JS绔箣闂寸殑閫氫俊鏄暣涓狿EDK IPC绯荤粺鐨勬牳蹇冿紝瀹炵幇浜嗗浐浠跺眰涓庡簲鐢ㄥ眰鐨勫弻鍚戞暟鎹氦鎹€€�

```
MFP鍥轰欢绔笌PEDK JS绔€氫俊鏋舵瀯:

鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹�                              MFP鍥轰欢绔� (Firmware Side)                          鈹�
鈹溾攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹�   闈㈡澘妯″潡       鈹�   鎵撳嵃妯″潡       鈹�   鎵弿妯″潡       鈹�      缃戠粶妯″潡                鈹�
鈹�  (Panel)        鈹�  (Print)        鈹�   (Scan)        鈹�     (Network)               鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹粹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹粹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹粹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
          鈹�                 鈹�                 鈹�                     鈹�
          鈻�                 鈻�                 鈻�                     鈻�
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹�                           PEDK Manager                                         鈹�
鈹�                      (娑堟伅灏佽涓庤矾鐢卞眰)                                          鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
                          鈹� Protocol Packet 灏佽
                          鈻�
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹�                        Transport Layer                                         鈹�
鈹�                      (浼犺緭灞傛娊璞℃帴鍙�)                                            鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
                          鈹� IPC閫氫俊 (Socket/SHM/MsgQ)
                          鈻�
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹�                        PEDK Runtime                                            鈹�
鈹�                      (杩愯鏃剁幆澧�)                                                鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
                          鈹� 娑堟伅瑙ｆ瀽涓庡垎鍙�
                          鈻�
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹�                        Bridge Module                                           鈹�
鈹�                      (C-JS妗ユ帴妯″潡)                                             鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
                          鈹� QuickJS璋冪敤
                          鈻�
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹�                        JavaScript App                                          鈹�
鈹�                      (PEDK搴旂敤绋嬪簭)                                             鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
```

### 12.2 娑堟伅鏍煎紡涓庡崗璁眰娆�

#### 12.2.1 Protocol Packet (鍗忚鍖呭眰)
```c
struct protocol_packet {
    uint8_t cmd;        // 鍛戒护绫诲瀷 (CMD_PRINTER_TO_APP/CMD_APP_TO_PRINTER)
    uint8_t len_high;   // 鏁版嵁闀垮害楂樺瓧鑺�
    uint8_t len_low;    // 鏁版嵁闀垮害浣庡瓧鑺�
    uint8_t data[0];    // 鏁版嵁杞借嵎 (鍖呭惈Socket Message)
};
```

**鍏抽敭鐗圭偣**:
- **鍥哄畾3瀛楄妭澶撮儴**: 纭繚鍗忚瑙ｆ瀽鐨勪竴鑷存€�
- **闀垮害缂栫爜**: 鏀寔鏈€澶�65535瀛楄妭鐨勬暟鎹紶杈�
- **鍛戒护鍒嗙被**: 鍖哄垎涓婅(APP鈫扢FP)鍜屼笅琛�(MFP鈫扐PP)娑堟伅

#### 12.2.2 Socket Message (娑堟伅杞借嵎灞�)
```c
struct socket_msg {
    uint8_t rtid;       // 杩愯鏃禝D (鏍囪瘑鐩爣JS搴旂敤)
    MAIN_MSG main;      // 涓绘秷鎭被鍨� (妯″潡鏍囪瘑)
    SUB_MSG sub;        // 瀛愭秷鎭被鍨� (鍏蜂綋鎿嶄綔)
    int32_t respond;    // 鍝嶅簲鐮� (鎴愬姛/澶辫触鐘舵€�)
    int32_t size;       // 涓氬姟鏁版嵁闀垮害
    uint8_t data[0];    // 涓氬姟鏁版嵁
};
```

**鍏抽敭鐗圭偣**:
- **杩愯鏃惰矾鐢�**: rtid鏀寔澶氬簲鐢ㄥ苟鍙戣繍琛�
- **妯″潡鍖栬璁�**: main/sub娑堟伅绫诲瀷鏀寔妯″潡鍖栨墿灞�
- **鐘舵€佸弽棣�**: respond瀛楁鎻愪緵鎿嶄綔缁撴灉鍙嶉
- **鐏垫椿杞借嵎**: 鏀寔浠绘剰鏍煎紡鐨勪笟鍔℃暟鎹�

### 12.3 閫氫俊娴佺▼璇︾粏鍒嗘瀽

#### 12.3.1 MFP鈫扟S閫氫俊娴佺▼ (涓嬭娑堟伅)

```
MFP鈫扟S閫氫俊娴佺▼ (涓嬭娑堟伅) - 鍥轰欢鍒癑avaScript搴旂敤鐨勫畬鏁存秷鎭紶閫掗摼璺�
鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺�

馃摛 姝ラ1: 娑堟伅浜х敓 (Message Generation)
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹� 鈿� 纭欢浜嬩欢 鈹€鈹€馃敟鉃★笍 浜嬩欢瑙﹀彂 鈹€鈹€鉃★笍 馃敡 PEDK Manager                                鈹�
鈹� (鎸夐敭/鐘舵€佸彉鍖�)                    (鍥轰欢绔鐞嗗櫒)                                鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�

馃摝 姝ラ2: 娑堟伅灏佽 (Message Packaging)
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹� 馃敡 PEDK Manager 鈹€鈹€馃摝鉃★笍 message_packing() 鈹€鈹€鉃★笍 馃摠 Socket Message                鈹�
鈹�                                                (rtid+main+sub)                  鈹�
鈹�                                                                                 鈹�
鈹�                                  猬囷笍 鍗忚灏佽                                     鈹�
鈹�                                                                                 鈹�
鈹� 馃搵 Protocol Packet 猬咃笍馃摝 protocol_packing() 猬咃笍 馃摠 Socket Message               鈹�
鈹� (cmd+len+data)                                                                 鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�

馃殌 姝ラ3: 浼犺緭灞傚彂閫� (Transport Layer Send)
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹� 馃搵 Protocol Packet 鈹€鈹€馃殌鉃★笍 transport_send() 鈹€鈹€鉃★笍 馃寪 Transport Layer            鈹�
鈹�                                                   (IPC鏈哄埗)                     鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�

馃摜 姝ラ4: 杩愯鏃舵帴鏀� (Runtime Receive)
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹� 馃寪 Transport Layer 鈹€鈹€馃摜鉃★笍 transport_receive() 鈹€鈹€鉃★笍 鈿欙笍 PEDK Runtime            鈹�
鈹�                                                      (涓诲惊鐜�)                   鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�

馃幆 姝ラ5: 娑堟伅鍒嗗彂 (Message Dispatch)
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹� 鈿欙笍 PEDK Runtime 鈹€鈹€馃幆鉃★笍 exec_p2a() 鈹€鈹€鉃★笍 馃寜 Bridge Module                       鈹�
鈹�                                         send_to_bridge()                       鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�

馃摫 姝ラ6: JS璋冪敤 (JavaScript Call)
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹� 馃寜 Bridge Module 鈹€鈹€馃摫鉃★笍 JS_Call() 鈹€鈹€鉃★笍 馃摫 JavaScript App                      鈹�
鈹�                                         bridge.on_msg()                        鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�

璇︾粏娴佺▼璇存槑:
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹�     姝ラ        鈹�                        澶勭悊鍐呭                             鈹�
鈹溾攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹尖攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹� 馃摛 娑堟伅浜х敓      鈹� 纭欢浜嬩欢瑙﹀彂銆佺姸鎬佸彉鍖栨娴嬨€佺敤鎴锋搷浣滄崟鑾�                     鈹�
鈹� 馃摝 娑堟伅灏佽      鈹� 鏁版嵁缁撴瀯鍖栥€佸崗璁牸寮忓寲銆佹秷鎭ご娣诲姞                           鈹�
鈹� 馃殌 浼犺緭灞傚彂閫�    鈹� IPC鏈哄埗閫夋嫨銆佹暟鎹紶杈撱€侀敊璇鐞�                             鈹�
鈹� 馃摜 杩愯鏃舵帴鏀�    鈹� 娑堟伅鎺ユ敹銆佹暟鎹В鏋愩€佹牸寮忛獙璇�                                 鈹�
鈹� 馃幆 娑堟伅鍒嗗彂      鈹� 鐩爣璇嗗埆銆佽矾鐢遍€夋嫨銆佹ā鍧楄皟搴�                                 鈹�
鈹� 馃摫 JS璋冪敤        鈹� 妗ユ帴杞崲銆佸洖璋冩墽琛屻€佸紓姝ュ鐞�                                 鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹粹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�

鍏抽敭鎶€鏈偣:
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹� 馃敡 PEDK Manager: 鍥轰欢绔秷鎭鐞嗗櫒锛岃礋璐ｇ‖浠朵簨浠剁殑鎹曡幏鍜屽垵姝ュ鐞�                  鈹�
鈹� 馃摠 Socket Message: 鍖呭惈杩愯鏃禝D(rtid)銆佷富娑堟伅绫诲瀷(main)銆佸瓙娑堟伅绫诲瀷(sub)        鈹�
鈹� 馃搵 Protocol Packet: 鏍囧噯鍗忚鍖呮牸寮忥紝鍖呭惈鍛戒护(cmd)銆侀暱搴�(len)銆佹暟鎹�(data)       鈹�
鈹� 馃寪 Transport Layer: 浼犺緭灞傛娊璞★紝鏀寔澶氱IPC鏈哄埗(Socket銆佸叡浜唴瀛樼瓑)            鈹�
鈹� 鈿欙笍 PEDK Runtime: 杩愯鏃剁幆澧冿紝绠＄悊搴旂敤鐢熷懡鍛ㄦ湡鍜屾秷鎭垎鍙�                        鈹�
鈹� 馃寜 Bridge Module: JavaScript-C妗ユ帴妯″潡锛屽疄鐜拌瑷€闂寸殑鏁版嵁杞崲                  鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
```

#### 12.3.2 JS鈫扢FP閫氫俊娴佺▼ (涓婅娑堟伅)

```
JS鈫扢FP閫氫俊娴佺▼ (涓婅娑堟伅) - JavaScript搴旂敤鍒板浐浠剁殑瀹屾暣娑堟伅浼犻€掗摼璺�
鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺�

馃摫 姝ラ1: JS璋冪敤 (JavaScript Call)
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹� 馃摫 JavaScript App 鈹€鈹€馃摫鉃★笍 bridge.send_msg() 鈹€鈹€鉃★笍 馃寜 Bridge Module              鈹�
鈹� (搴旂敤灞傝皟鐢�)                                      js_send_msg()                 鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�

馃摝 姝ラ2: 娑堟伅灏佽 (Message Packaging)
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹� 馃寜 Bridge Module 鈹€鈹€馃摝鉃★笍 a2p_msg_send() 鈹€鈹€鉃★笍 馃彮 make_a2p_msg()                鈹�
鈹�                                                (娣诲姞rtid)                       鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�

馃殌 姝ラ3: 浼犺緭灞傚彂閫� (Transport Layer Send)
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹� 馃彮 make_a2p_msg() 鈹€鈹€馃殌鉃★笍 transport_send() 鈹€鈹€鉃★笍 馃寪 Transport Layer             鈹�
鈹�                                                  浼犺緭灞�                         鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�

馃摜 姝ラ4: MFP鎺ユ敹澶勭悊 (MFP Receive Processing)
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹� 馃寪 Transport Layer 鈹€鈹€馃摜鉃★笍 IPC鎺ユ敹 鈹€鈹€鉃★笍 馃敡 PEDK Manager                        鈹�
鈹�                                         鍥轰欢绔鐞嗗櫒                            鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�

锟� 姝ラ5: 涓氬姟澶勭悊 (Business Processing)
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹� 馃敡 PEDK Manager 鈹€鈹€馃幆鉃★笍 娑堟伅璺敱 鈹€鈹€鉃★笍 馃幆 鐩爣妯″潡                              鈹�
鈹�                                        (Print/Scan绛�)                          鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�

璇︾粏娴佺▼璇存槑:
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹�     姝ラ        鈹�                        澶勭悊鍐呭                             鈹�
鈹溾攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹尖攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹� 馃摫 JS璋冪敤        鈹� JavaScript搴旂敤璋冪敤bridge鎺ュ彛銆佸弬鏁伴獙璇併€佹暟鎹噯澶�             鈹�
鈹� 馃摝 娑堟伅灏佽      鈹� 娣诲姞杩愯鏃禝D(rtid)銆佹秷鎭被鍨嬫爣璇嗐€佹暟鎹牸寮忓寲                 鈹�
鈹� 馃殌 浼犺緭灞傚彂閫�    鈹� 閫夋嫨浼犺緭鏈哄埗銆佹暟鎹簭鍒楀寲銆佺綉缁滃彂閫�                           鈹�
鈹� 馃摜 MFP鎺ユ敹澶勭悊   鈹� 鏁版嵁鎺ユ敹銆佸崗璁В鏋愩€佹秷鎭獙璇�                                 鈹�
鈹� 馃幆 涓氬姟澶勭悊      鈹� 娑堟伅璺敱銆佹ā鍧楄皟搴︺€佷笟鍔￠€昏緫鎵ц                             鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹粹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�

鍏抽敭鎶€鏈偣:
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹� 馃摫 JavaScript App: 搴旂敤灞備笟鍔￠€昏緫锛岄€氳繃bridge鎺ュ彛涓庡簳灞傞€氫俊                     鈹�
鈹� 馃寜 Bridge Module: JavaScript-C妗ユ帴妯″潡锛岃礋璐ｆ暟鎹被鍨嬭浆鎹㈠拰鎺ュ彛閫傞厤             鈹�
鈹� 馃彮 make_a2p_msg(): A2P娑堟伅鏋勯€犲嚱鏁帮紝娣诲姞杩愯鏃舵爣璇嗗拰娑堟伅澶翠俊鎭�                 鈹�
鈹� 馃寪 Transport Layer: 浼犺緭灞傛娊璞★紝鏀寔Socket銆佸叡浜唴瀛樼瓑澶氱IPC鏈哄埗              鈹�
鈹� 馃敡 PEDK Manager: 鍥轰欢绔秷鎭鐞嗗櫒锛岃礋璐ｆ秷鎭帴鏀躲€佽В鏋愬拰璺敱鍒嗗彂                鈹�
鈹� 馃幆 鐩爣妯″潡: 鍏蜂綋鐨勪笟鍔″鐞嗘ā鍧楋紝濡傛墦鍗版ā鍧椼€佹壂鎻忔ā鍧楃瓑                        鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�

娑堟伅鏍煎紡璇存槑:
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹� A2P娑堟伅缁撴瀯 (App to Printer):                                                  鈹�
鈹� 鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�   鈹�
鈹� 鈹�  rtid   鈹�  main   鈹�   sub   鈹� respond 鈹�  size   鈹�         data            鈹�   鈹�
鈹� 鈹� (2瀛楄妭) 鈹� (2瀛楄妭) 鈹� (2瀛楄妭) 鈹� (4瀛楄妭) 鈹� (4瀛楄妭) 鈹�      (鍙彉闀垮害)         鈹�   鈹�
鈹� 鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹粹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹粹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹粹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹粹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹粹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�   鈹�
鈹�                                                                                 鈹�
鈹� - rtid: 杩愯鏃禝D锛屾爣璇嗙洰鏍囧簲鐢ㄥ疄渚�                                              鈹�
鈹� - main: 涓绘秷鎭被鍨嬶紝瀹氫箟娑堟伅鐨勫ぇ绫诲埆                                            鈹�
鈹� - sub: 瀛愭秷鎭被鍨嬶紝瀹氫箟鍏蜂綋鐨勬搷浣滅被鍨�                                           鈹�
鈹� - respond: 鍝嶅簲鏍囪瘑锛岀敤浜庤姹�-鍝嶅簲妯″紡                                          鈹�
鈹� - size: 鏁版嵁闀垮害锛屾寚绀篸ata瀛楁鐨勫瓧鑺傛暟                                          鈹�
鈹� - data: 涓氬姟鏁版嵁锛屽彲浠ユ槸JSON銆佷簩杩涘埗绛変换鎰忔牸寮�                                  鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
```

### 12.4 鍏抽敭鎶€鏈疄鐜�

#### 12.4.1 Bridge妯″潡鏍稿績瀹炵幇
```c
// C鈫扟S鏁版嵁浼犻€�
void send_to_bridge(PeSFRunTime* prt, uint16_t data_length, const char* msg)
{
    // 1. 鍒涘缓ArrayBuffer鍖呰C鏁版嵁
    JSValue value = JS_NewArrayBufferCopy(prt->qjs_ctx,
                                         (const unsigned char*)msg,
                                         data_length);

    // 2. 鑾峰彇JS绔痓ridge瀵硅薄
    JSValue global = JS_GetGlobalObject(prt->qjs_ctx);
    JSValue bridge = JS_GetPropertyStr(prt->qjs_ctx, global, "bridge");

    // 3. 璋冪敤JS鍥炶皟鍑芥暟
    JSValue on_msg = JS_GetPropertyStr(prt->qjs_ctx, bridge, "on_msg");
    if (JS_IsFunction(prt->qjs_ctx, on_msg)) {
        JS_Call(prt->qjs_ctx, on_msg, bridge, 1, &value);
    }
}

// JS鈫扖娑堟伅鍙戦€�
JSValue js_send_msg(JSContext* ctx, JSValueConst this_val,
                   int argc, JSValueConst* argv)
{
    PeSFRunTime* prt = GET_PESF_RUNTIME(ctx);
    const char* str = JS_ToCStringLen(ctx, &str_len, argv[0]);

    // 璋冪敤a2p娑堟伅鍙戦€�
    a2p_msg_send(prt->dynamic_property.rtid, (uint16_t)str_len, (uint8_t*)str);

    return JS_UNDEFINED;
}
```

#### 12.4.2 娑堟伅灏佽涓庤В鏋�
```c
// MFP绔秷鎭皝瑁�
static struct socket_msg* message_packing(MAIN_MSG_E main, SUB_MSG_E sub,
                                         int32_t respond, uint8_t* buf, int32_t bufsize)
{
    struct socket_msg* msg = malloc(sizeof(struct socket_msg) + bufsize);

    msg->rtid    = 0x00;   // 杩愯鏃禝D
    msg->main    = main;   // 涓绘秷鎭被鍨�
    msg->sub     = sub;    // 瀛愭秷鎭被鍨�
    msg->respond = respond;// 鍝嶅簲鐮�
    msg->size    = bufsize;// 鏁版嵁闀垮害

    if (bufsize > 0 && buf != NULL) {
        memcpy(msg->data, buf, bufsize);
    }

    return msg;
}

// 鍗忚鍖呭皝瑁�
static struct protocol_packet* protocol_packing(uint8_t cmd, uint8_t* buf, uint16_t bufsize)
{
    struct protocol_packet* packet = malloc(sizeof(struct protocol_packet) + bufsize);

    packet->cmd      = cmd;                           // 鍛戒护绫诲瀷
    packet->len_high = (uint8_t)((bufsize & 0xFF00) >> 8); // 闀垮害楂樺瓧鑺�
    packet->len_low  = (uint8_t)(bufsize & 0xFF);     // 闀垮害浣庡瓧鑺�

    if (bufsize > 0 && buf != NULL) {
        memcpy(packet->data, buf, bufsize);
    }

    return packet;
}
```

#### 12.4.3 JS绔秷鎭В鏋�
```javascript
// JS绔秷鎭帴鏀跺鐞�
bridge.on_msg = (info) => {
    // 瑙ｆ瀽ArrayBuffer鏁版嵁
    let dataView = new DataView(info);

    // 瑙ｆ瀽娑堟伅澶撮儴 (鎸夊ぇ绔簭)
    let value1 = swap32(dataView.getInt32(0));  // rtid + main
    let value2 = swap32(dataView.getInt32(4));  // sub + respond
    let value3 = swap32(dataView.getInt32(8));  // size
    let value4 = swap32(dataView.getInt32(12)); // 淇濈暀瀛楁

    // 瑙ｆ瀽涓氬姟鏁版嵁
    let businessData = '';
    for(let i = 0; i < dataView.byteLength - 16; i++) {
        businessData += String.fromCharCode(dataView.getUint8(i + 16));
    }

    // 鍒嗗彂浜嬩欢鍒板叿浣撳鐞嗗櫒
    let instance = new ObserverManager();
    instance.notify(value1, value2, value3, value4, businessData);
};

// 瀛楄妭搴忚浆鎹㈠嚱鏁�
function swap32(val) {
    return ((val & 0xFF) << 24) |
           ((val & 0xFF00) << 8) |
           ((val >> 8) & 0xFF00) |
           ((val >> 24) & 0xFF);
}
```

### 12.5 閫氫俊鐗规€у垎鏋�

#### 12.5.1 澶氬簲鐢ㄦ敮鎸�
- **杩愯鏃禝D鏈哄埗**: 姣忎釜JS搴旂敤鍒嗛厤鍞竴鐨剅tid
- **娑堟伅璺敱**: 鏍规嵁rtid灏嗘秷鎭矾鐢卞埌姝ｇ‘鐨勫簲鐢ㄥ疄渚�
- **骞挎挱鏀寔**: rtid=0鏃舵敮鎸佸箍鎾秷鎭埌鎵€鏈夊簲鐢�

#### 12.5.2 寮傛閫氫俊
- **浜嬩欢闃熷垪**: 浣跨敤浜嬩欢闃熷垪閬垮厤鏃跺簭娣蜂贡
- **闈為樆濉炲彂閫�**: transport_send()閲囩敤闈為樆濉炴柟寮�
- **鍥炶皟鏈哄埗**: JS绔€氳繃鍥炶皟鍑芥暟澶勭悊寮傛娑堟伅

#### 12.5.3 鏁版嵁鏍煎紡鐏垫椿鎬�
- **浜岃繘鍒舵暟鎹�**: 鏀寔浠绘剰浜岃繘鍒舵暟鎹紶杈�
- **JSON鏀寔**: 涓氬姟灞傚彲浣跨敤JSON鏍煎紡
- **澶у皬绔鐞�**: JS绔鐞嗗瓧鑺傚簭杞崲

#### 12.5.4 閿欒澶勭悊涓庡閿�
- **鎻℃墜鏈哄埗**: 鍚姩鏃惰繘琛宲ing-pong鎻℃墜纭杩炴帴
- **瓒呮椂澶勭悊**: transport_receive()鏀寔瓒呮椂鏈哄埗
- **鐘舵€佸弽棣�**: respond瀛楁鎻愪緵鎿嶄綔缁撴灉鍙嶉

### 12.6 鎻℃墜鍗忚涓庤繛鎺ュ缓绔�

#### 12.6.1 鎻℃墜娴佺▼
```
PEDK Runtime鍚姩鏃剁殑鎻℃墜娴佺▼ - 杩炴帴寤虹珛涓庣姸鎬佺‘璁�
鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺�

馃彄 姝ラ1: 鍙戦€丳ING (Send PING)
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹� 鈿欙笍 PEDK Runtime 鈹€鈹€馃彄鉃★笍 make_ping_pkt() 鈹€鈹€鉃★笍 馃敡 PEDK Manager                   鈹�
鈹� (杩愯鏃剁幆澧�)                                  (鍥轰欢绔鐞嗗櫒)                     鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�

馃彄 姝ラ2: 杩斿洖PONG (Return PONG)
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹� 馃敡 PEDK Manager 鈹€鈹€馃彄鉃★笍 CMD_PONG_PACKET 鈹€鈹€鉃★笍 鈿欙笍 PEDK Runtime                   鈹�
鈹� (鍥轰欢绔鐞嗗櫒)                                (杩愯鏃剁幆澧�)                       鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�

鉁� 姝ラ3: 杩炴帴纭 (Connection Confirmation)
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹� 鈿欙笍 PEDK Runtime 鈹€鈹€鉁呪灐锔� 璁剧疆杩炴帴鐘舵€� 鈹€鈹€鉃★笍 馃敆 姝ｅ父閫氫俊                           鈹�
鈹� (杩愯鏃剁幆澧�)                              (杩炴帴寤虹珛鎴愬姛)                        鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�

鎻℃墜鍗忚璇︾粏璇存槑:
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹�     姝ラ        鈹�                        澶勭悊鍐呭                             鈹�
鈹溾攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹尖攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹� 馃彄 鍙戦€丳ING      鈹� Runtime鍚姩鏃朵富鍔ㄥ彂閫丳ING鍖咃紝鍖呭惈杩愯鏃禝D                   鈹�
鈹� 馃彄 杩斿洖PONG      鈹� Manager鎺ユ敹PING鍚庣珛鍗宠繑鍥濸ONG鍖咃紝纭杩炴帴鍙敤               鈹�
鈹� 鉁� 杩炴帴纭      鈹� Runtime鎺ユ敹PONG鍚庤缃繛鎺ョ姸鎬侊紝寮€濮嬫甯搁€氫俊                 鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹粹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�

PING/PONG鍖呮牸寮�:
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹� PING鍖呯粨鏋�:                                                                     鈹�
鈹� 鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�                                       鈹�
鈹� 鈹�   cmd   鈹� len_hi  鈹� len_lo  鈹�  rtid   鈹�                                       鈹�
鈹� 鈹� (1瀛楄妭) 鈹� (1瀛楄妭) 鈹� (1瀛楄妭) 鈹� (1瀛楄妭) 鈹�                                       鈹�
鈹� 鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹粹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹粹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹粹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�                                       鈹�
鈹�                                                                                 鈹�
鈹� - cmd: CMD_PING_PACKET (0x01)                                                  鈹�
鈹� - len_hi: 鏁版嵁闀垮害楂樺瓧鑺� (0x00)                                                鈹�
鈹� - len_lo: 鏁版嵁闀垮害浣庡瓧鑺� (0x01)                                                鈹�
鈹� - rtid: 杩愯鏃禝D锛屾爣璇嗗彂閫佹柟                                                    鈹�
鈹�                                                                                 鈹�
鈹� PONG鍖呯粨鏋�: 涓嶱ING鍖呯浉鍚岋紝浣哻md瀛楁涓篊MD_PONG_PACKET (0x02)                    鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�

鎻℃墜鏈哄埗鐗圭偣:
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹� 馃攧 涓诲姩鎻℃墜: Runtime鍚姩鏃朵富鍔ㄥ彂璧凤紝纭繚杩炴帴鐨勫強鏃跺缓绔�                          鈹�
鈹� 鈿� 蹇€熷搷搴�: Manager绔嬪嵆鍝嶅簲PING锛屽噺灏戣繛鎺ュ缓绔嬪欢杩�                              鈹�
鈹� 馃洝锔� 杩炴帴楠岃瘉: 閫氳繃PING-PONG纭鍙屽悜閫氫俊閫氶亾鐨勫彲鐢ㄦ€�                             鈹�
鈹� 馃攳 鐘舵€佺鐞�: Runtime鏍规嵁PONG鍝嶅簲璁剧疆鍐呴儴杩炴帴鐘舵€佹爣蹇�                           鈹�
鈹� 馃搳 閿欒妫€娴�: 瓒呮椂鏈敹鍒癙ONG鍒欒涓鸿繛鎺ュけ璐ワ紝瑙﹀彂閲嶈繛鏈哄埗                        鈹�
鈹� 馃敀 瀹夊叏鎬�: 鎻℃墜鍖呭惈杩愯鏃禝D锛岄槻姝㈣繛鎺ユ贩涔�                                      鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
```

#### 12.6.2 PING鍖呮牸寮�
```c
// PING鍖呭垱寤哄嚱鏁�
int make_ping_pkt(uint8_t* buff_out, uint16_t* length_out, uint8_t rtid)
{
    buff_out[0] = CMD_PING_PACKET;  // 鍛戒护绫诲瀷
    buff_out[1] = 0x00;             // 闀垮害楂樺瓧鑺�
    buff_out[2] = 0x01;             // 闀垮害浣庡瓧鑺� (1瀛楄妭鏁版嵁)
    buff_out[3] = rtid;             // 杩愯鏃禝D

    *length_out = 4;                // 鎬婚暱搴�4瀛楄妭
    return 0;
}
```

### 12.7 PEDK Manager鏍稿績瀹炵幇鍒嗘瀽

#### 12.7.1 鏋舵瀯璁捐涓庤亴璐�

PEDK Manager (`pedk_mgr.c`) 鏄疢FP鍥轰欢绔殑鏍稿績缁勪欢锛岃礋璐ｏ細
- **Socket鏈嶅姟鍣ㄧ鐞�**: 鐩戝惉鏉ヨ嚜PEDK Runtime鐨勮繛鎺�
- **娑堟伅闃熷垪绠＄悊**: 缁存姢鍙屽悜娑堟伅闃熷垪
- **妯″潡娉ㄥ唽鏈哄埗**: 鏀寔鍚勪笟鍔℃ā鍧楃殑鍔ㄦ€佹敞鍐�
- **浜嬩欢绯荤粺闆嗘垚**: 涓庡浐浠朵簨浠剁鐞嗗櫒闆嗘垚
- **鍗忚澶勭悊**: 澶勭悊鎻℃墜銆佸簲鐢ㄧ敓鍛藉懆鏈熺鐞嗙瓑鍗忚

```
PEDK Manager鍐呴儴鏋舵瀯 - 鍥轰欢绔牳蹇冪粍浠剁殑瀹屾暣鏋舵瀯璁捐
鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺愨晲鈺�

馃彈锔� PEDK Manager 鏍稿績鏋舵瀯 (鍥涘ぇ鏍稿績缁勪欢)
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹� 馃攲 Socket鏈嶅姟鍣�    馃搵 娑堟伅闃熷垪绠＄悊    馃摑 妯″潡娉ㄥ唽琛�    鈿� 浜嬩欢绠＄悊鍣�            鈹�
鈹� (TCP:50999)       (鍙屽悜闃熷垪)         (Handler琛�)     (Event Callback)          鈹�
鈹� server_thread     s_msg_list         s_reg_list      event_handler             鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
                                       猬囷笍 猬囷笍 猬囷笍 猬囷笍
                                缁熶竴姹囪仛鍒版牳蹇冨鐞嗙嚎绋�

鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹�                          馃攧 鏍稿績澶勭悊绾跨▼                                        鈹�
鈹�                      (server_thread 涓诲惊鐜�)                                    鈹�
鈹�                parse_socket_packet() + message_dispatch()                      鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
                                       猬囷笍
                                  鍗忚瑙ｆ瀽涓庡垎鍙�

鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹�                          馃幆 涓氬姟妯″潡澶勭悊                                        鈹�
鈹�                                                                                 鈹�
鈹� 馃枿锔� Print Module    馃搫 Scan Module    馃枼锔� Panel Module    馃寪 Network Module      鈹�
鈹� (鎵撳嵃澶勭悊)         (鎵弿澶勭悊)        (闈㈡澘澶勭悊)         (缃戠粶澶勭悊)              鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�

缁勪欢璇︾粏璇存槑:
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹�     缁勪欢        鈹�                        鍔熻兘鑱岃矗                             鈹�
鈹溾攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹尖攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹� 馃攲 Socket鏈嶅姟鍣�  鈹� TCP鏈嶅姟鍣�(绔彛50999)銆佸鎴风杩炴帴绠＄悊銆佹暟鎹敹鍙�             鈹�
鈹� 馃搵 娑堟伅闃熷垪绠＄悊  鈹� 鍙屽悜娑堟伅闃熷垪銆佺嚎绋嬪畨鍏ㄦ搷浣溿€佹秷鎭紦鍐蹭笌璋冨害                   鈹�
鈹� 锟� 妯″潡娉ㄥ唽琛�    鈹� 涓氬姟妯″潡鍔ㄦ€佹敞鍐屻€丠andler鍑芥暟绠＄悊銆佹秷鎭矾鐢辫〃               鈹�
鈹� 鈿� 浜嬩欢绠＄悊鍣�    鈹� 绯荤粺浜嬩欢闆嗘垚銆佸洖璋冨嚱鏁扮鐞嗐€佸紓姝ヤ簨浠跺鐞�                     鈹�
鈹� 馃攧 鏍稿績澶勭悊绾跨▼  鈹� 涓讳簨浠跺惊鐜€佸崗璁В鏋愩€佹秷鎭垎鍙戙€侀敊璇鐞�                     鈹�
鈹� 馃幆 涓氬姟妯″潡      鈹� 鍏蜂綋涓氬姟閫昏緫銆佺‖浠舵搷浣溿€佺姸鎬佺鐞嗐€佸搷搴旂敓鎴�                   鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹粹攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�

鏋舵瀯鐗圭偣鍒嗘瀽:
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹� 馃攧 鍗曠嚎绋嬫ā鍨�: 鏍稿績澶勭悊閲囩敤鍗曠嚎绋嬩簨浠跺惊鐜紝閬垮厤澶嶆潅鐨勭嚎绋嬪悓姝ラ棶棰�                鈹�
鈹� 馃搵 娑堟伅闃熷垪: 寮傛娑堟伅澶勭悊锛屾敮鎸侀珮骞跺彂鍜岀獊鍙戞祦閲�                                 鈹�
鈹� 馃攲 缃戠粶鎶借薄: TCP Socket鎻愪緵鍙潬鐨勮繘绋嬮棿閫氫俊閫氶亾                                鈹�
鈹� 馃摑 鍔ㄦ€佹敞鍐�: 鏀寔涓氬姟妯″潡鐨勫姩鎬佹敞鍐屽拰鍗歌浇锛屾彁楂樼郴缁熺伒娲绘€�                       鈹�
鈹� 鈿� 浜嬩欢椹卞姩: 鍩轰簬浜嬩欢鐨勫紓姝ュ鐞嗘ā寮忥紝鎻愰珮鍝嶅簲鎬ц兘                               鈹�
鈹� 馃幆 妯″潡鍖�: 娓呮櫚鐨勬ā鍧楄竟鐣岋紝渚夸簬缁存姢鍜屾墿灞�                                       鈹�
鈹� 锟斤笍 閿欒闅旂: 鍚勬ā鍧楃嫭绔嬪鐞嗭紝鍗曚釜妯″潡鏁呴殰涓嶅奖鍝嶆暣浣撶郴缁�                        鈹�
鈹� 馃搳 鐘舵€佺鐞�: 闆嗕腑鐨勮繛鎺ョ姸鎬佸拰娑堟伅鐘舵€佺鐞�                                       鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�

鏁版嵁娴佽浆璺緞:
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹� 鎺ユ敹璺緞: Socket鎺ユ敹 鈫� 娑堟伅闃熷垪 鈫� 鍗忚瑙ｆ瀽 鈫� 妯″潡璺敱 鈫� 涓氬姟澶勭悊                鈹�
鈹� 鍙戦€佽矾寰�: 涓氬姟妯″潡 鈫� 娑堟伅灏佽 鈫� 娑堟伅闃熷垪 鈫� Socket鍙戦€� 鈫� PEDK Runtime           鈹�
鈹� 浜嬩欢璺緞: 纭欢浜嬩欢 鈫� 浜嬩欢绠＄悊鍣� 鈫� 娑堟伅鐢熸垚 鈫� 闃熷垪澶勭悊 鈫� 搴旂敤閫氱煡               鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�

鍏抽敭鎶€鏈疄鐜�:
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹� 馃敀 绾跨▼瀹夊叏: pthread_mutex_t淇濇姢鍏变韩鏁版嵁缁撴瀯                                   鈹�
鈹� 馃摝 鍐呭瓨绠＄悊: 缁熶竴鐨勫唴瀛樺垎閰嶅拰閲婃斁鏈哄埗锛岄槻姝㈠唴瀛樻硠婕�                             鈹�
鈹� 馃攳 閿欒澶勭悊: 瀹屽杽鐨勯敊璇娴嬪拰鎭㈠鏈哄埗                                           鈹�
鈹� 鈿� 鎬ц兘浼樺寲: 閾捐〃鎿嶄綔浼樺寲銆佹秷鎭壒澶勭悊銆侀浂鎷疯礉鎶€鏈�                               鈹�
鈹� 馃搱 鍙墿灞曟€�: 妯″潡鍖栬璁℃敮鎸佸姛鑳芥墿灞曞拰绯荤粺鍗囩骇                                   鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
```

#### 12.7.2 鍏抽敭鏁版嵁缁撴瀯

```c
// 娑堟伅闃熷垪鑺傜偣
struct packet_node {
    struct list_head list;           // 閾捐〃鑺傜偣
    struct protocol_packet* msg;     // 鍗忚鍖呮寚閽�
};

// 妯″潡娉ㄥ唽琛ㄩ」
struct module_table {
    struct list_head list;           // 閾捐〃鑺傜偣
    MAIN_MSG_E main;                // 涓绘秷鎭被鍨�
    PEDKAPI_HANDLER_FUNC func;      // 澶勭悊鍑芥暟鎸囬拡
    void* arg;                      // 涓婁笅鏂囧弬鏁�
};

// 鍏ㄥ眬鐘舵€佺鐞�
static struct list_head s_msg_list = PI_LIST_HEAD_INIT(s_msg_list);  // 娑堟伅闃熷垪
static pthread_mutex_t s_msg_lock = PTHREAD_MUTEX_INITIALIZER;       // 娑堟伅闃熷垪閿�
static struct list_head s_reg_list = PI_LIST_HEAD_INIT(s_reg_list);  // 娉ㄥ唽琛�
static pthread_mutex_t s_reg_lock = PTHREAD_MUTEX_INITIALIZER;       // 娉ㄥ唽琛ㄩ攣
static PI_THREAD_T s_serv_tid = INVALIDTHREAD;                       // 鏈嶅姟绾跨▼ID
```

#### 12.7.3 娑堟伅闃熷垪鏈哄埗

**鍏ラ槦鎿嶄綔 (message_queue_push)**:
```c
static int32_t message_queue_push(struct protocol_packet* packet)
{
    struct packet_node* node;

    // 1. 鍙傛暟楠岃瘉
    RETURN_VAL_IF(packet == NULL, pi_log_e, -1);

    // 2. 鍒嗛厤鑺傜偣鍐呭瓨
    node = (struct packet_node *)pi_zalloc(sizeof(struct packet_node));
    RETURN_VAL_IF(node == NULL, pi_log_e, -1);

    // 3. 璁剧疆鑺傜偣鏁版嵁
    node->msg = packet;

    // 4. 绾跨▼瀹夊叏鍦版坊鍔犲埌闃熷垪灏鹃儴
    MUTEX_PROCESS(pi_list_add_tail(&node->list, &s_msg_list), s_msg_lock);

    return 0;
}
```

**鍑洪槦鎿嶄綔 (message_queue_pop)**:
```c
static struct protocol_packet* message_queue_pop(void)
{
    struct packet_node* node = NULL;
    struct protocol_packet* packet = NULL;
    struct list_head* pos;

    // 绾跨▼瀹夊叏鍦颁粠闃熷垪澶撮儴鍙栧嚭娑堟伅
    MUTEX_PROCESS({
        if (!pi_list_empty(&s_msg_list)) {
            pos = s_msg_list.next;
            node = pi_list_entry(pos, struct packet_node, list);
            pi_list_del_entry(&node->list);
        }
    }, s_msg_lock);

    if (node != NULL) {
        packet = node->msg;
        pi_free(node);  // 閲婃斁鑺傜偣锛屼絾淇濈暀娑堟伅鏁版嵁
    }

    return packet;
}
```

#### 12.7.4 Socket鏈嶅姟鍣ㄥ疄鐜�

**鏈嶅姟鍣ㄤ富寰幆**:
```c
static void* server_thread(void* arg)
{
    int serv_sock, clnt_sock;
    struct sockaddr_in serv_addr, clnt_addr;

    // 1. 鍒涘缓Socket
    serv_sock = socket(PF_INET, SOCK_STREAM, 0);

    // 2. 缁戝畾鍦板潃 (127.0.0.1:50999)
    memset(&serv_addr, 0, sizeof(serv_addr));
    serv_addr.sin_family = AF_INET;
    serv_addr.sin_addr.s_addr = inet_addr(SERVER_ADDR);
    serv_addr.sin_port = htons(SERVER_PORT);
    bind(serv_sock, (struct sockaddr*)&serv_addr, sizeof(serv_addr));

    // 3. 鐩戝惉杩炴帴
    listen(serv_sock, 5);

    // 4. 鎺ュ彈杩炴帴
    socklen_t clnt_addr_len = sizeof(clnt_addr);
    clnt_sock = accept(serv_sock, (struct sockaddr*)&clnt_addr, &clnt_addr_len);

    // 5. 璁剧疆闈為樆濉炴ā寮�
    fcntl(clnt_sock, F_SETFL, O_NONBLOCK);

    // 6. 涓婚€氫俊寰幆
    while(1) {
        // 6.1 鍙戦€侀槦鍒椾腑鐨勬秷鎭�
        struct protocol_packet* sendMsg = message_queue_pop();
        if (sendMsg != NULL) {
            uint16_t msg_len = ((sendMsg->len_high << 8) & 0xFF00) |
                              (sendMsg->len_low & 0xFF);
            write(clnt_sock, sendMsg, sizeof(struct protocol_packet) + msg_len);
            free(sendMsg);
        }

        // 6.2 鎺ユ敹鏉ヨ嚜Runtime鐨勬秷鎭�
        struct protocol_packet recvStruct;
        int reamin_len = read(clnt_sock, &recvStruct, sizeof(struct protocol_packet));

        if (reamin_len == sizeof(struct protocol_packet)) {
            // 瑙ｆ瀽骞跺鐞嗗崗璁寘
            protocol_packet_handler(&recvStruct, clnt_sock);
        }
    }
}
```

#### 12.7.5 鍗忚澶勭悊鏈哄埗

**鍛戒护鍒嗗彂澶勭悊**:
```c
// 鍗忚鍖呭鐞嗗嚱鏁� (鍦╯erver_thread涓诲惊鐜腑)
switch(recvStruct.cmd)
{
    case CMD_PING:  // PING鎻℃墜
        // 鍝嶅簲PONG缁橰untime
        pedk_mgr_send_cmd_to_runenv(CMD_PING, NULL, 0);
        if(msgLen != 0) {
            pi_log_d("ping cmd error\n");
        }
        app_init();  // 鍒濆鍖栧簲鐢ㄧ鐞嗗櫒
        break;

    case CMD_APP_START_RETURN:    // 搴旂敤鍚姩鍝嶅簲
    case CMD_APP_END_RETURN:      // 搴旂敤缁撴潫鍝嶅簲
    case CMD_APP_PAUSE_RETURN:    // 搴旂敤鏆傚仠鍝嶅簲
    case CMD_APP_CONTINUE_RETURN: // 搴旂敤缁х画鍝嶅簲
        // 楠岃瘉娑堟伅闀垮害
        if(msgLen != 2 && msgLen != 1) {
            error_handing("CMD_APP_END_RETURN cmd error");
        }
        // 璇诲彇鍝嶅簲鏁版嵁骞跺鐞�
        break;

    case CMD_APP_TO_PRINTER:  // 搴旂敤鍒版墦鍗版満娑堟伅
        // 璇诲彇瀹屾暣娑堟伅鏁版嵁
        uint8_t* msgData = malloc(msgLen);
        read(clnt_sock, msgData, msgLen);

        // 瑙ｆ瀽Socket Message
        struct socket_msg* socketMsg = (struct socket_msg*)msgData;

        // 鍒嗗彂鍒板搴旂殑涓氬姟妯″潡
        parse_socket_packet(socketMsg);

        free(msgData);
        break;
}
```

**娑堟伅鍒嗗彂鍒颁笟鍔℃ā鍧�**:
```c
static void parse_socket_packet(struct socket_msg* info)
{
    PEDKAPI_HANDLER_FUNC func = NULL;
    struct module_table* node;
    struct list_head* pos;
    struct list_head* n;

    // 閬嶅巻娉ㄥ唽琛ㄦ煡鎵惧搴旂殑澶勭悊鍑芥暟
    pi_list_for_each_safe(pos, n, &s_reg_list)
    {
        node = pi_list_entry(pos, struct module_table, list);
        if (node != NULL && node->func != NULL && node->main == info->main)
        {
            func = node->func;
            break;
        }
    }

    // 璋冪敤涓氬姟妯″潡澶勭悊鍑芥暟
    if (func != NULL) {
        func(info->sub, info->respond, info->size, info->data, node->arg);
        pi_log_d("parse socket packet success\n");
    } else {
        pi_log_e("no search module(%d)\n", (int32_t)info->main);
    }
}
```

#### 12.7.6 妯″潡娉ㄥ唽鏈哄埗

**妯″潡娉ㄥ唽**:
```c
int32_t pedk_mgr_register_handler(MAIN_MSG_E module, PEDKAPI_HANDLER_FUNC handler, void* ctx)
{
    struct module_table* node;

    // 1. 鍒嗛厤娉ㄥ唽琛ㄨ妭鐐�
    node = (struct module_table *)pi_zalloc(sizeof(struct module_table));
    RETURN_VAL_IF(node == NULL, pi_log_e, -1);

    // 2. 璁剧疆鑺傜偣淇℃伅
    node->main = module;    // 涓绘秷鎭被鍨� (妯″潡ID)
    node->func = handler;   // 澶勭悊鍑芥暟鎸囬拡
    node->arg = ctx;        // 涓婁笅鏂囧弬鏁�

    // 3. 绾跨▼瀹夊叏鍦版坊鍔犲埌娉ㄥ唽琛�
    MUTEX_PROCESS(pi_list_add_tail(&node->list, &s_reg_list), s_reg_lock);

    return 0;
}
```

**妯″潡娉ㄩ攢**:
```c
void pedk_mgr_unregister_handler(MAIN_MSG_E module)
{
    struct module_table* node;
    struct list_head* pos;
    struct list_head* n;

    // 閬嶅巻娉ㄥ唽琛ㄦ煡鎵惧苟鍒犻櫎瀵瑰簲妯″潡
    pi_list_for_each_safe(pos, n, &s_reg_list)
    {
        node = pi_list_entry(pos, struct module_table, list);
        if (node != NULL && node->main == module)
        {
            MUTEX_PROCESS(pi_list_del_entry(&node->list), s_reg_lock);
            pi_free(node);
        }
    }
}
```

#### 12.7.7 浜嬩欢绯荤粺闆嗘垚

**浜嬩欢鍥炶皟澶勭悊**:
```c
static void pedk_mgr_event_callback(const EVT_MSG_S* msg, void* ctx)
{
    uint32_t module_id = msg->module_id;
    uint32_t event_type = msg->event_type;

    pi_log_d("pedk mgr get module: %u request event type: %u \n", module_id, event_type);

    switch(event_type)
    {
        case EVT_TYPE_USBHOST_GET_ICCARD_INFO:  // IC鍗′俊鎭簨浠�
            pi_log_d("ICCARD msg->data:%s, lenth:%d\n", msg->data, msg->data_length);
            // 杞彂缁橮EDK Runtime
            pedk_mgr_send_msg_to_runenv(MSG_MOUDLE_USB_ICCARD, MSG_GET_ICCAED_INFO,
                                       0, msg->data, msg->data_length);
            break;

        case EVT_TYPE_SYSTEMSTATUS_UPDATE:      // 绯荤粺鐘舵€佹洿鏂颁簨浠�
            status_event_callback(msg->data, msg->data_length);
            break;

        default:
            pi_log_d("unknown event type: %u\n", event_type);
            break;
    }
}
```

**浜嬩欢娉ㄥ唽鍒濆鍖�**:
```c
EVT_MGR_CLI_S* pedk_mgr_event_register(void)
{
    // 1. 鍒涘缓浜嬩欢绠＄悊鍣ㄥ鎴风
    EVT_MGR_CLI_S* cli_ptr = pi_event_mgr_create_client(EVT_MODULE_PEDK_MGR,
                                                       pedk_mgr_event_callback,
                                                       NULL, NULL);
    if(cli_ptr == NULL) {
        return NULL;
    }

    // 2. 瀹氫箟鍏虫敞鐨勪簨浠剁被鍨�
    uint32_t modify_event_array[] = {
        EVT_TYPE_USBHOST_GET_ICCARD_INFO,  // IC鍗′簨浠�
        EVT_TYPE_SYSTEMSTATUS_UPDATE,      // 绯荤粺鐘舵€佷簨浠�
    };
    int32_t event_count = sizeof(modify_event_array) / sizeof(modify_event_array[0]);

    // 3. 娉ㄥ唽浜嬩欢鐩戝惉
    pi_event_mgr_register(cli_ptr, modify_event_array, event_count);

    return cli_ptr;
}
```

#### 12.7.8 鐢熷懡鍛ㄦ湡绠＄悊

**鍒濆鍖栨祦绋�**:
```c
int32_t pedk_mgr_prolog(void)
{
    int32_t ec = 1;

    // 1. 鍒濆鍖栧簲鐢ㄧ鐞嗗櫒
    app_manager_init();

    do {
        // 2. 鍒涘缓鏈嶅姟鍣ㄧ嚎绋�
        s_serv_tid = pi_thread_create(server_thread, PI_LARGE_STACK, NULL,
                                     PI_MEDIUM_PRIORITY, NULL, "server_thread");
        BREAK_IF(s_serv_tid == INVALIDTHREAD, pi_log_w);

        // 3. 娉ㄥ唽浜嬩欢绠＄悊鍣�
        g_pedk_cli_ptr = pedk_mgr_event_register();
        pi_log_d("pedk mgr event register ... \n");
        if(g_pedk_cli_ptr == NULL) {
            pi_log_e("pedk mgr event init error\n");
        }

        ec = 0;
    } while(0);

    if (ec != 0) {
        pi_log_e("pedk manager initialize failed(%d)", ec);
        pedk_mgr_epilog();
    }
    return ec;
}
```

**娓呯悊娴佺▼**:
```c
void pedk_mgr_epilog(void)
{
    // 1. 閿€姣佹湇鍔″櫒绾跨▼
    if (s_serv_tid != INVALIDTHREAD) {
        pi_thread_destroy(s_serv_tid);
        s_serv_tid = INVALIDTHREAD;
    }

    // 2. 閿€姣丷FID绾跨▼ (濡傛灉瀛樺湪)
    if (s_rfid_tid != INVALIDTHREAD) {
        pi_thread_destroy(s_rfid_tid);
        s_rfid_tid = INVALIDTHREAD;
    }

    // 3. 娓呯悊娑堟伅闃熷垪
    message_queue_clean();
}
```

## 13. 瀹屾暣閫氫俊娴佺▼鎶€鏈垎鏋�

### 13.1 閫氫俊娴佺▼姒傝堪

鏈珷璇︾粏鍒嗘瀽浠嶫avaScript搴旂敤璋冪敤鍒癕FP鍥轰欢锛屽啀杩斿洖鍒癙EDK鐨勫畬鏁撮€氫俊娴佺▼銆傝繖涓祦绋嬪睍绀轰簡PEDK IPC绯荤粺鐨勬牳蹇冩妧鏈疄鐜帮紝娑夊強澶氫釜灞傛鐨勬暟鎹浆鎹€€佸崗璁皝瑁呭拰娑堟伅璺敱銆�

**瀹屾暣娴佺▼閾捐矾**:
```
JS App 鈫� bridge.send_msg() 鈫� js_send_msg() 鈫� a2p_msg_send() 鈫� transport_send()
鈫� Socket浼犺緭 鈫� PEDK Manager 鈫� 妯″潡娉ㄥ唽澶勭悊 鈫� pedk_mgr_send_msg_to_runenv()
鈫� Socket杩斿洖 鈫� RecvMsgToMfp() 鈫� bridge.on_msg() 鈫� JS App
```

### 13.2 娴佺▼绗竴闃舵锛欽avaScript鍒癈妗ユ帴

#### 13.2.1 JavaScript搴旂敤灞傝皟鐢�

**鎶€鏈疄鐜�**:
```javascript
// JavaScript搴旂敤灞傝皟鐢ㄧず渚�
bridge.send_msg("Hello MFP");
```

**鍏抽敭鎶€鏈偣**:
- **QuickJS寮曟搸**: 鎻愪緵JavaScript杩愯鐜
- **鍏ㄥ眬bridge瀵硅薄**: 浣滀负JS涓嶤閫氫俊鐨勫敮涓€鍏ュ彛
- **瀛楃涓插弬鏁�**: 鏀寔浠绘剰鏍煎紡鐨勫瓧绗︿覆鏁版嵁浼犻€�

#### 13.2.2 Bridge妯″潡澶勭悊 (js_send_msg)

```c
JSValue js_send_msg(JSContext* ctx, JSValueConst this_val, int argc, JSValueConst* argv)
{
    PeSFRunTime* prt = GET_PESF_RUNTIME(ctx);
    const char* str = NULL;
    size_t str_len = 0;

    LOG_D("bridge","js_send_msg");
    if (!JS_IsString(argv[0])) {
        return JS_UNDEFINED;
    }
    str = JS_ToCStringLen(ctx, &str_len, argv[0]);

    a2p_msg_send(prt->dynamic_property.rtid, (uint16_t)str_len, (uint8_t*)str);

    return JS_UNDEFINED;
}
```

**鎶€鏈垎鏋�**:
- **鍙傛暟楠岃瘉**: `JS_IsString()` 纭繚鍙傛暟绫诲瀷姝ｇ‘
- **瀛楃涓茶浆鎹�**: `JS_ToCStringLen()` 灏咼SValue杞崲涓篊瀛楃涓�
- **杩愯鏃禝D**: `prt->dynamic_property.rtid` 鏍囪瘑褰撳墠搴旂敤瀹炰緥
- **鏁版嵁浼犻€�**: 璋冪敤 `a2p_msg_send()` 杩涜涓嬩竴姝ュ鐞�

#### 13.2.3 A2P娑堟伅灏佽 (a2p_msg_send)

```c
static int32_t a2p_msg_send(uint8_t rtid, uint16_t data_length, uint8_t* data)
{
    // 姝ゅ娉ㄦ剰锛屽洜涓哄app绾跨▼瀛樺湪鍏卞悓浣跨敤澶栭儴浼犺緭鎺ュ彛鐨勬椂鏈猴紝鎵€浠ュ湪鍙戦€佸墠瑕佸姞閿併€�
    // 1.鍒朵綔鍙戦€佹秷鎭�
    uint16_t msg_length;
    uint8_t* sendbuf = (uint8_t*)malloc(data_length + 3);
    make_a2p_msg(sendbuf, &msg_length, data, data_length, rtid);
    // 2.鍙戦€�
    transport_send(sendbuf, msg_length);

    free(sendbuf);
}
```

**鎶€鏈垎鏋�**:
- **鍐呭瓨鍒嗛厤**: `malloc(data_length + 3)` 涓烘秷鎭ご棰勭暀绌洪棿
- **娑堟伅鏋勯€�**: `make_a2p_msg()` 娣诲姞鍗忚澶翠俊鎭�
- **浼犺緭灞傝皟鐢�**: `transport_send()` 杩涜瀹為檯鏁版嵁鍙戦€�
- **鍐呭瓨绠＄悊**: 鍙婃椂閲婃斁涓存椂缂撳啿鍖�

### 13.3 娴佺▼绗簩闃舵锛氫紶杈撳眰澶勭悊

#### 13.3.1 浼犺緭灞傚彂閫� (transport_send)

**鎶€鏈疄鐜�**:
- **鎶借薄鎺ュ彛**: 鏀寔澶氱浼犺緭鏂瑰紡锛圫ocket銆佸叡浜唴瀛樼瓑锛�
- **鏁版嵁搴忓垪鍖�**: 灏嗙粨鏋勫寲鏁版嵁杞崲涓哄瓧鑺傛祦
- **閿欒澶勭悊**: 浼犺緭澶辫触鏃剁殑閲嶈瘯鍜岄敊璇姤鍛婃満鍒�

#### 13.3.2 Socket閫氫俊

**鎶€鏈壒鐐�**:
- **TCP杩炴帴**: 浣跨敤鍙潬鐨凾CP鍗忚纭繚鏁版嵁瀹屾暣鎬�
- **绔彛50999**: 鍥哄畾绔彛鐢ㄤ簬PEDK Runtime涓嶮FP Manager閫氫俊
- **闃诲/闈為樆濉�**: 鏀寔鍚屾鍜屽紓姝ヤ紶杈撴ā寮�

### 13.4 娴佺▼绗笁闃舵锛歁FP鍥轰欢绔鐞�

#### 13.4.1 PEDK Manager鎺ユ敹

**Socket鏈嶅姟鍣ㄦ帴鏀�**:
```c
// 鏈嶅姟鍣ㄤ富寰幆鎺ユ敹鏁版嵁
while (1) {
    clnt_sock = accept(serv_sock, (struct sockaddr*)&clnt_addr, &clnt_addr_size);
    // 鎺ユ敹鍗忚鍖�
    recv(clnt_sock, &packet, sizeof(packet), 0);
    // 瑙ｆ瀽骞跺鐞�
    parse_socket_packet(&packet);
}
```

**鎶€鏈垎鏋�**:
- **澶氬鎴风鏀寔**: accept()寰幆澶勭悊澶氫釜PEDK Runtime杩炴帴
- **鍗忚瑙ｆ瀽**: `parse_socket_packet()` 瑙ｆ瀽鎺ユ敹鍒扮殑鏁版嵁鍖�
- **娑堟伅闃熷垪**: 灏嗚В鏋愬悗鐨勬秷鎭姞鍏ュ鐞嗛槦鍒�

#### 13.4.2 妯″潡娉ㄥ唽涓庤矾鐢�

```c
int32_t pedk_mgr_register_handler(MAIN_MSG_E module, PEDKAPI_HANDLER_FUNC handler, void* ctx)
{
    struct module_table* entry;

    entry = (struct module_table*)pi_zalloc(sizeof(struct module_table));
    RETURN_VAL_IF(entry == NULL, pi_log_e, -1);

    entry->main = module;
    entry->func = handler;
    entry->arg = ctx;

    MUTEX_PROCESS(pi_list_add_tail(&entry->list, &s_reg_list), s_reg_lock);

    return 0;
}
```

**鎶€鏈垎鏋�**:
- **鍔ㄦ€佹敞鍐�**: 涓氬姟妯″潡鍙湪杩愯鏃舵敞鍐屽鐞嗗嚱鏁�
- **绾跨▼瀹夊叏**: 浣跨敤mutex淇濇姢娉ㄥ唽琛ㄦ搷浣�
- **鍑芥暟鎸囬拡**: 閫氳繃鍑芥暟鎸囬拡瀹炵幇娑堟伅鍒嗗彂

### 13.5 娴佺▼绗洓闃舵锛氫笟鍔℃ā鍧楀鐞�

#### 13.5.1 娑堟伅澶勭悊绀轰緥

**鐘舵€佹ā鍧楀鐞�**:
```c
case MSG_STATUS_SUB:
case MSG_STATUS_SUB_LIST_LEN:
    ret = (char *)pi_malloc(MAX_STATUS_LEN * MAX_STATUS_NUM);
    pi_memset(ret, 0x00, MAX_STATUS_LEN * MAX_STATUS_NUM);
    get_pedkstatuslist((char*)buf, &len, ret);
    pi_log_d("Get list or list type: {%s} return string [%s]\n", buf, ret);
    iret = pedk_mgr_send_msg_to_runenv(MSG_MODULE_STATUS, sub, len, (unsigned char *)ret, pi_strlen(ret) + 1);
```

**鎶€鏈垎鏋�**:
- **涓氬姟閫昏緫**: 鏍规嵁娑堟伅绫诲瀷鎵ц鐩稿簲鐨勪笟鍔″鐞�
- **鏁版嵁鍑嗗**: 鍑嗗杩斿洖缁橮EDK Runtime鐨勫搷搴旀暟鎹�
- **鍝嶅簲鍙戦€�**: 璋冪敤 `pedk_mgr_send_msg_to_runenv()` 鍙戦€佸搷搴�

### 13.6 娴佺▼绗簲闃舵锛氬搷搴旇繑鍥�

#### 13.6.1 MFP鍒癙EDK鐨勫搷搴� (pedk_mgr_send_msg_to_runenv)

```c
int32_t pedk_mgr_send_msg_to_runenv(MAIN_MSG_E main, SUB_MSG_E sub, int32_t respond, uint8_t* buf, int32_t bufsize)
{
    struct socket_msg*  msg;
    int32_t             ret;

    msg = message_packing(main, sub, respond, buf, bufsize);
    RETURN_VAL_IF(msg == NULL, pi_log_e, -1);

    ret = message_queue_push(protocol_packing(CMD_PRINTER_TO_APP, (uint8_t *)msg, sizeof(struct socket_msg) + bufsize));
    pi_free(msg);

    return ret;
}
```

**鎶€鏈垎鏋�**:
- **娑堟伅灏佽**: `message_packing()` 鍒涘缓鏍囧噯鐨剆ocket娑堟伅鏍煎紡
- **鍗忚鍖呰**: `protocol_packing()` 娣诲姞浼犺緭灞傚崗璁ご
- **闃熷垪澶勭悊**: 灏嗗搷搴旀秷鎭姞鍏ュ彂閫侀槦鍒�
- **鍐呭瓨绠＄悊**: 鍙婃椂閲婃斁涓存椂娑堟伅缁撴瀯

#### 13.6.2 PEDK绔帴鏀跺搷搴� (RecvMsgToMfp)

```c
int RecvMsgToMfp(MAIN_MSG main, SUB_MSG sub, int* respond, unsigned char *recvBuf, int *recvBufSize, int timeout)
{
    struct timespec start,end;
    clock_gettime(CLOCK_MONOTONIC, &start);

    // 浠庢秷鎭槦鍒楅噷闈㈡壘娑堟伅
    do{
        if((m_queue.size > 0) && (0 ==pop_sync_msg(main, sub, respond, recvBuf, recvBufSize)))
        {
            return 0;
        }
        clock_gettime(CLOCK_MONOTONIC, &end);
        usleep(10000);//绋嶅井绔欎竴涓嬪氨琛�
    }while( (end.tv_sec - start.tv_sec) < timeout);

    printf("Client recv timeout\n");
    return -1;
}
```

**鎶€鏈垎鏋�**:
- **瓒呮椂鏈哄埗**: 浣跨敤 `clock_gettime()` 瀹炵幇绮剧‘鐨勮秴鏃舵帶鍒�
- **杞妫€鏌�**: 瀹氭湡妫€鏌ユ秷鎭槦鍒椾腑鏄惁鏈夊尮閰嶇殑鍝嶅簲
- **娑堟伅鍖归厤**: `pop_sync_msg()` 鏍规嵁main/sub绫诲瀷鍖归厤娑堟伅
- **閿欒澶勭悊**: 瓒呮椂杩斿洖-1锛屾垚鍔熻繑鍥�0

### 13.7 娴佺▼绗叚闃舵锛氳繑鍥濲avaScript

#### 13.7.1 C鍒癑avaScript鐨勬暟鎹紶閫� (send_to_bridge)

```c
void send_to_bridge(PeSFRunTime* prt, uint16_t data_length, const char* msg)
{
    JSValue value = JS_NewArrayBufferCopy(prt->qjs_ctx, (const unsigned char*)msg, data_length);
    JSValue argv[] = { value };

    JSValue global = JS_GetGlobalObject(prt->qjs_ctx);
    JSValue bridge = JS_GetPropertyStr(prt->qjs_ctx, global, "bridge");

    if (!JS_IsObject(bridge)) {
        LOG_E("bridge","bridge is not object");
        return;
    }

    JSValue on_msg = JS_GetPropertyStr(prt->qjs_ctx, bridge, "on_msg");
    if (JS_IsFunction(prt->qjs_ctx, on_msg)) {
        LOG_D("bridge","befor bridge call");
        JS_Call(prt->qjs_ctx, on_msg, bridge, sizeof(argv)/sizeof(argv[0]), argv);
    }
}
```

**鎶€鏈垎鏋�**:
- **鏁版嵁鍖呰**: `JS_NewArrayBufferCopy()` 灏咰鏁版嵁鍖呰涓篔avaScript ArrayBuffer
- **瀵硅薄鑾峰彇**: 鑾峰彇鍏ㄥ眬bridge瀵硅薄鍜宱n_msg鍥炶皟鍑芥暟
- **鍑芥暟璋冪敤**: `JS_Call()` 璋冪敤JavaScript鍥炶皟鍑芥暟
- **鍐呭瓨绠＄悊**: 鑷姩绠＄悊JSValue鐨勭敓鍛藉懆鏈�

#### 13.7.2 JavaScript绔帴鏀跺鐞�

```javascript
// JavaScript绔敞鍐屽洖璋冨嚱鏁�
bridge.on_msg = function(data) {
    // 澶勭悊浠嶮FP杩斿洖鐨勬暟鎹�
    const view = new DataView(data);
    const result = new TextDecoder().decode(data);
    console.log("Received from MFP:", result);

    // 鎵ц涓氬姟閫昏緫
    handleMfpResponse(result);
};
```

**鎶€鏈垎鏋�**:
- **鏁版嵁瑙ｆ瀽**: 浣跨敤DataView鍜孴extDecoder瑙ｆ瀽ArrayBuffer
- **鍥炶皟鏈哄埗**: 閫氳繃娉ㄥ唽鍥炶皟鍑芥暟澶勭悊寮傛鍝嶅簲
- **涓氬姟闆嗘垚**: 灏嗗搷搴旀暟鎹泦鎴愬埌搴旂敤涓氬姟閫昏緫涓�

### 13.8 js_send_msg() vs SendMsgToMfp() 鍏崇郴瑙ｆ瀽

#### 13.8.1 涓や釜鍑芥暟鐨勬湰璐ㄥ尯鍒�

杩欐槸涓€涓潪甯搁噸瑕佺殑姒傚康锛乣js_send_msg()` 鍜� `SendMsgToMfp()` 鏄湪**涓嶅悓灞傛**鍜�**涓嶅悓浣跨敤鍦烘櫙**涓嬬殑涓や釜鍑芥暟锛�

**鏋舵瀯灞傛瀵规瘮**:
```
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹�                           馃彈锔� PEDK 绯荤粺鏋舵瀯灞傛                                  鈹�
鈹溾攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹� 馃摫 JavaScript搴旂敤灞�                                                             鈹�
鈹�    鈫� bridge.send_msg()                                                         鈹�
鈹� 馃寜 Bridge妗ユ帴灞� 鈹€鈹€鉃★笍 js_send_msg() 鈹€鈹€鉃★笍 a2p_msg_send()                        鈹�
鈹�    鈫� transport_send()                                                          鈹�
鈹� 馃殌 浼犺緭灞�                                                                       鈹�
鈹�    鈫� Socket/IPC                                                                鈹�
鈹� 馃彮 MFP鍥轰欢绔�                                                                    鈹�
鈹�    鈫� PEDK Manager                                                              鈹�
鈹� 馃摝 涓氬姟妯″潡灞�                                                                   鈹�
鈹溾攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹� 馃攧 **鍙︿竴鏉¤矾寰勶細C API鐩存帴璋冪敤**                                                鈹�
鈹� 馃搵 C API灞� 鈹€鈹€鉃★笍 SendMsgToMfp() 鈹€鈹€鉃★笍 packetProtocolStruct()                    鈹�
鈹�    鈫� Socket鍙戦€�                                                                鈹�
鈹� 馃彮 MFP鍥轰欢绔�                                                                    鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
```

#### 13.8.2 js_send_msg() - Bridge妗ユ帴鍑芥暟

**浣嶇疆**: `pedk/src/runtime/modules/bridge/bridge.c`
**浣滅敤**: JavaScript鍒癈鐨勬ˉ鎺ュ叆鍙�

```c
JSValue js_send_msg(JSContext* ctx, JSValueConst this_val, int argc, JSValueConst* argv)
{
    PeSFRunTime* prt = GET_PESF_RUNTIME(ctx);
    const char* str = JS_ToCStringLen(ctx, &str_len, argv[0]);

    // 鍏抽敭锛氳皟鐢╝2p_msg_send锛岃€屼笉鏄洿鎺ュ彂閫�
    a2p_msg_send(prt->dynamic_property.rtid, (uint16_t)str_len, (uint8_t*)str);

    return JS_UNDEFINED;
}
```

**鐗圭偣**:
- 鉁� **QuickJS缁戝畾鍑芥暟**: 涓撻棬涓篔avaScript璋冪敤璁捐
- 鉁� **杩愯鏃朵笂涓嬫枃**: 鍖呭惈rtid锛堣繍琛屾椂ID锛変俊鎭�
- 鉁� **寮傛澶勭悊**: 閫氳繃transport灞傚紓姝ュ彂閫�
- 鉁� **鏁版嵁灏佽**: 鑷姩娣诲姞A2P鍗忚澶�

#### 13.8.3 SendMsgToMfp() - C API鐩存帴鍑芥暟

**浣嶇疆**: `pedk/src/API/event/PEDK_event.c`
**浣滅敤**: C浠ｇ爜鐩存帴璋冪敤鐨凙PI鍑芥暟

```c
int SendMsgToMfp(MAIN_MSG main, SUB_MSG sub, int respond, int size, const unsigned char *param)
{
    int ret=-1;
    while(1)
    {
        if(clientWriteFlag == 0)
        {
            //鍔犻攣
            pthread_mutex_lock(&mutex);
            //灏佽
            sendInfo = packetProtocolStruct(main, sub, respond, size, (unsigned char *)param, 1);
            clientWriteFlag = 1;
            //瑙ｉ攣
            pthread_mutex_unlock(&mutex);
            ret = 0;
            break;
        }
        usleep(10000);
    }
    return ret;
}
```

**鐗圭偣**:
- 鉁� **C API鍑芥暟**: 渚汣浠ｇ爜鐩存帴璋冪敤
- 鉁� **鍚屾鍙戦€�**: 闃诲绛夊緟鍙戦€佸畬鎴�
- 鉁� **娑堟伅绫诲瀷鏄庣‘**: 闇€瑕佹寚瀹歁AIN_MSG鍜孲UB_MSG
- 鉁� **绾跨▼瀹夊叏**: 浣跨敤mutex淇濇姢鍙戦€佹搷浣�

#### 13.8.4 浣跨敤鍦烘櫙瀵规瘮

**js_send_msg() 浣跨敤鍦烘櫙**:
```javascript
// JavaScript搴旂敤涓娇鐢�
bridge.send_msg("Hello from JS App");
```
- 馃幆 **JavaScript搴旂敤**: 鐢ㄦ埛缂栧啓鐨凧S搴旂敤璋冪敤
- 馃幆 **鍔ㄦ€佹暟鎹�**: 杩愯鏃跺姩鎬佺敓鎴愮殑鏁版嵁
- 馃幆 **鐢ㄦ埛浜や簰**: 鍝嶅簲鐢ㄦ埛鎿嶄綔鐨勬暟鎹彂閫�

**SendMsgToMfp() 浣跨敤鍦烘櫙**:
```c
// C妯″潡涓娇鐢�
SendMsgToMfp(MSG_MODULE_JOBCTL, MSG_JOBCTL_SUB_START, 0, strlen(data), data);
```
- 馃幆 **C API妯″潡**: 濡俲obctl銆乶et銆乻tatus绛夋ā鍧�
- 馃幆 **绯荤粺鍔熻兘**: 鎵撳嵃銆佺綉缁溿€佺姸鎬佹煡璇㈢瓑绯荤粺绾у姛鑳�
- 馃幆 **鍥哄畾鍗忚**: 棰勫畾涔夌殑娑堟伅绫诲瀷鍜屾牸寮�

#### 13.8.5 鏁版嵁娴佸悜瀵规瘮

**js_send_msg() 鏁版嵁娴�**:
```
JavaScript App
    鈫� bridge.send_msg()
js_send_msg() [Bridge灞俔
    鈫� a2p_msg_send()
transport_send() [浼犺緭灞俔
    鈫� Socket
PEDK Manager [MFP绔痌
    鈫� 妯″潡璺敱
Business Module [涓氬姟澶勭悊]
```

**SendMsgToMfp() 鏁版嵁娴�**:
```
C API Module
    鈫� SendMsgToMfp()
packetProtocolStruct() [鍗忚灏佽]
    鈫� Socket鐩存帴鍙戦€�
PEDK Manager [MFP绔痌
    鈫� 妯″潡璺敱
Business Module [涓氬姟澶勭悊]
```

#### 13.8.6 瀹為檯浠ｇ爜绀轰緥瀵规瘮

**JavaScript璋冪敤绀轰緥**:
```javascript
// 鐢ㄦ埛JS搴旂敤
function sendUserData() {
    const userData = JSON.stringify({
        action: "print",
        data: "Hello World"
    });
    bridge.send_msg(userData);  // 璋冪敤js_send_msg()
}
```

**C API璋冪敤绀轰緥**:
```c
// C妯″潡浠ｇ爜
void start_print_job() {
    PRINT_JOB_INFO job_info = {
        .copies = 1,
        .paper_size = A4
    };

    // 鐩存帴璋冪敤SendMsgToMfp()
    SendMsgToMfp(MSG_MODULE_JOBCTL,
                 MSG_JOBCTL_SUB_START,
                 0,
                 sizeof(job_info),
                 (unsigned char*)&job_info);
}
```

#### 13.8.7 鍏抽敭鍖哄埆鎬荤粨

| 鐗规€� | js_send_msg() | SendMsgToMfp() |
|------|---------------|----------------|
| **璋冪敤鑰�** | JavaScript搴旂敤 | C API妯″潡 |
| **鏁版嵁绫诲瀷** | 瀛楃涓� | 缁撴瀯鍖栨暟鎹� |
| **鍗忚灞�** | A2P鍗忚 | Socket鍗忚 |
| **杩愯鏃朵俊鎭�** | 鍖呭惈rtid | 鏃爎tid |
| **鍙戦€佹柟寮�** | 寮傛 | 鍚屾闃诲 |
| **浣跨敤鍦烘櫙** | 鐢ㄦ埛搴旂敤 | 绯荤粺鍔熻兘 |
| **娑堟伅鏍煎紡** | 鑷敱鏍煎紡 | 鍥哄畾鏍煎紡 |

### 13.9 鍏抽敭鎶€鏈€荤粨

#### 13.9.1 鏁版嵁杞崲鎶€鏈�

```
瀹屾暣鏁版嵁杞崲閾捐矾:
鈹屸攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
鈹� JavaScript String 鈫� C char* 鈫� A2P Message 鈫� Protocol Packet 鈫� Socket Data      鈹�
鈹� 鈫�                                                                               鈹�
鈹� Socket Data 鈫� Protocol Packet 鈫� Socket Message 鈫� Business Data 鈫� Response      鈹�
鈹� 鈫�                                                                               鈹�
鈹� Response 鈫� Protocol Packet 鈫� Socket Data 鈫� ArrayBuffer 鈫� JavaScript Data       鈹�
鈹斺攢鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹€鈹�
```

**鍏抽敭杞崲鍑芥暟**:
- `JS_ToCStringLen()`: JavaScript瀛楃涓茶浆C瀛楃涓�
- `make_a2p_msg()`: 鍒涘缓A2P娑堟伅鏍煎紡
- `message_packing()`: 鍒涘缓Socket娑堟伅鏍煎紡
- `protocol_packing()`: 鍒涘缓鍗忚鍖呮牸寮�
- `JS_NewArrayBufferCopy()`: C鏁版嵁杞琂avaScript ArrayBuffer

#### 13.9.2 鍚屾鏈哄埗鎶€鏈�

**绾跨▼瀹夊叏淇濇姢**:
```c
// 娑堟伅闃熷垪淇濇姢
pthread_mutex_t s_msg_lock = PTHREAD_MUTEX_INITIALIZER;
MUTEX_PROCESS(pi_list_add_tail(&node->list, &s_msg_list), s_msg_lock);

// 妯″潡娉ㄥ唽琛ㄤ繚鎶�
pthread_mutex_t s_reg_lock = PTHREAD_MUTEX_INITIALIZER;
MUTEX_PROCESS(pi_list_add_tail(&entry->list, &s_reg_list), s_reg_lock);
```

**鍚屾閫氫俊鏈哄埗**:
- **璇锋眰-鍝嶅簲妯″紡**: SendMsgToMfp() + RecvMsgToMfp() 閰嶅浣跨敤
- **瓒呮椂鎺у埗**: 闃叉鏃犻檺绛夊緟锛屾彁楂樼郴缁熷仴澹€�
- **娑堟伅鍖归厤**: 閫氳繃main/sub绫诲瀷纭繚鍝嶅簲鐨勬纭尮閰�

#### 13.9.3 閿欒澶勭悊鎶€鏈�

**澶氬眰閿欒妫€鏌�**:
```c
// 鍙傛暟楠岃瘉
RETURN_VAL_IF(packet == NULL, pi_log_e, -1);

// 鍐呭瓨鍒嗛厤妫€鏌�
node = (struct packet_node *)pi_zalloc(sizeof(struct packet_node));
RETURN_VAL_IF(node == NULL, pi_log_e, -1);

// 鍑芥暟璋冪敤妫€鏌�
if (!JS_IsFunction(prt->qjs_ctx, on_msg)) {
    LOG_E("bridge","on_msg is not function");
    return;
}
```

**閿欒鎭㈠鏈哄埗**:
- **鍐呭瓨娓呯悊**: 鍙婃椂閲婃斁鍒嗛厤鐨勫唴瀛樿祫婧�
- **杩炴帴閲嶅缓**: Socket杩炴帴鏂紑鏃剁殑閲嶈繛鏈哄埗
- **鐘舵€侀噸缃�**: 閿欒鍙戠敓鏃堕噸缃浉鍏崇姸鎬�

## 14. 鎬荤粨涓庡睍鏈�

PEDK IPC绯荤粺鏄竴涓璁＄簿鑹殑杩涚▼闂撮€氫俊妗嗘灦锛屽叿鏈変互涓嬬壒鐐癸細

**鎶€鏈紭鍔�**:
- 鍒嗗眰鏋舵瀯娓呮櫚锛屾ā鍧楄亴璐ｆ槑纭�
- 澶氱IPC鏈哄埗骞跺瓨锛岄€傚簲涓嶅悓鍦烘櫙闇€姹�
- 瀹屽杽鐨勭嚎绋嬪畨鍏ㄦ満鍒朵繚璇佺郴缁熺ǔ瀹氭€�
- 鐏垫椿鐨勬秷鎭矾鐢辨敮鎸佸鏉傜殑涓氬姟閫昏緫

**鏀硅繘鏂瑰悜**:
- 杩涗竴姝ヤ紭鍖栨€ц兘锛屽噺灏戞秷鎭紶閫掑欢杩�
- 澧炲己閿欒澶勭悊鍜屾仮澶嶈兘鍔�
- 瀹屽杽鐩戞帶鍜岃瘖鏂姛鑳�
- 鎻愬崌绯荤粺鐨勫彲鎵╁睍鎬у拰鍙淮鎶ゆ€�

璇ョ郴缁熶负宓屽叆寮忚澶囦笂鐨勫鏉傚簲鐢ㄦ彁渚涗簡寮哄ぇ鐨勯€氫俊鍩虹璁炬柦锛屾槸鐜颁唬宓屽叆寮忚蒋浠舵灦鏋勭殑浼樼瀹炶返銆�

---

*鏈姤鍛婂熀浜嶱EDK IPC绯荤粺婧愮爜娣卞害鍒嗘瀽锛岃缁嗛槓杩颁簡绯荤粺鏋舵瀯銆佹牳蹇冩満鍒躲€佸疄鐜扮粏鑺傜瓑鍏抽敭鎶€鏈鐐癸紝涓虹郴缁熶紭鍖栥€佺淮鎶ゅ拰鎵╁睍鎻愪緵鍏ㄩ潰鐨勬妧鏈弬鑰冦€�*
