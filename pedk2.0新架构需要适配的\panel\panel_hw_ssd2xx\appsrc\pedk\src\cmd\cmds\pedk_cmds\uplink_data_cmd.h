#ifndef _UPLINK_DATA_CMD_H_
#define _UPLINK_DATA_CMD_H_

#include "cmd/cmds/pedk_cmds/pedk_cmds.h"
#include "cmd/exec/pedk_cmd_exec.h"

typedef struct UplinkDataCmdFormat {
    uint32_t type;        // 指令类型
    std::string target_app_name; // APP
    std::string msg;      // 消息

    MSGPACK_DEFINE(type, target_app_name, msg);
} UplinkDataCmdFormat;

class UplinkDataCmd: public PedkCmd{
public:
    UplinkDataCmd();
    UplinkDataCmd(std::string target_app_name, std::string msg);
    ~UplinkDataCmd();
    uint32_t execute(PedkCmdExec *pedk_cmd_exec) override;
    std::string serialize() override;
    uint32_t deserialize(std::string ss) override;
protected:
private:
    UplinkDataCmdFormat m_uplink_data_cmd;
};

#endif // _UPLINK_DATA_CMD_H__