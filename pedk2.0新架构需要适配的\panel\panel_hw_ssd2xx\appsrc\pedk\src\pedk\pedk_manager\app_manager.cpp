#include "app_manager.h"
#include "basic/os/os.h"
#include "basic/config.h"
#include <iostream>
#include "pedk/pedk_basic/pedk_json_io.h"
#include "basic/utils/file_utils.h"
#include <algorithm>

AppManager* AppManager::m_instance = nullptr;

AppManager::AppManager()
{
    m_pedk_enable = false;
}

AppManager::~AppManager()
{
}

AppManager* AppManager::get_instance()
{
    if (m_instance == nullptr) {
        m_instance = new AppManager();
    }

    return m_instance;
}

void AppManager::add_app(App app)
{
    m_apps.insert(std::make_pair(app.m_app_name, app));
}

std::map<std::string, App>& AppManager::get_apps()
{
    return m_apps;
}

bool AppManager::find_app(std::string app_name,App *app)
{
    bool ret = true;

    std::map<std::string, App>::iterator it;
    it = m_apps.find(app_name);

    if(it != m_apps.end()){
       *app = it->second;
    }else{
        app = nullptr;
        ret = false;
    }

    return ret;
}

bool AppManager::delete_app(std::string app_name)
{
    std::map<std::string, App>::iterator it;
    it = m_apps.find(app_name);
    if (it == m_apps.end()) {
        return false;
    }

    m_apps.erase(it);

    return true;
}

std::string AppManager::check_hearbeat()
{
    // LOG_D("Check heartbeat");

    std::string ret = "";

    time_t curr_time = OS::get_systime();
    time_t diff_time;
    time_t last_time;

    // 遍历全部app
    std::map<std::string, App>::iterator it;
    for (it = m_apps.begin(); it != m_apps.end(); ++it) {
        // 计算最后一次心跳更新时间
        last_time = it->second.m_os_information.m_hearbeat_time;
        diff_time = curr_time - last_time;
        //如果大于差值大于30秒，认为异常
        if (diff_time > ABNORMAL_TIME) {
            //LOG_E("Abnormal heartbeat:%s", it->second.m_app_name.c_str());
            ret = it->second.m_app_name;
            break;
        }
    }

    return ret;
}

void AppManager::update_heartbeat(std::string app_name)
{
    // LOG_D("update_heartbeat");
    if (m_apps.find(app_name) != m_apps.end()) {
        m_apps[app_name].update_heartbeat();
    }
}

void AppManager::check_sys_app()
{
    // 1.检查系统APP路径，获取系统APP列表
    std::vector<std::string> sys_app_list = OS::get_directories(PEDK_SYS_APP_WORKSPACE_PATH);
    // 2.检查普通APP路径，获取普通APP列表
    std::vector<std::string> common_app_list = OS::get_directories(PEDK_APP_WORKSPACE_PATH);

    // 3.逐一查找系统APP，看再普通APP列表中是否存在
    std::vector<std::string>::iterator it;
    for (it = sys_app_list.begin(); it != sys_app_list.end(); ++it) {
        auto result = std::find(common_app_list.begin(), common_app_list.end(), *it);
        if (result == common_app_list.end()) {
            // 4.如果不存在，则将此APP整个文件夹拷贝过去。
            LOG_D("Copy sys app <%s> to workspace!", it->c_str());
            std::string src_folder = PEDK_SYS_APP_WORKSPACE_PATH + *it;
            std::string target_folder = PEDK_APP_WORKSPACE_PATH + *it;
            OS::copy_app(target_folder, src_folder);
        }else {
            LOG_D("Cover sys app <%s> to workspace!", it->c_str());
            std::string src_folder = PEDK_SYS_APP_WORKSPACE_PATH + *it;
            std::string target_folder = PEDK_APP_WORKSPACE_PATH + *it;
            OS::copy_app(target_folder, src_folder);
        }
    }
}

void AppManager::auto_start_app()
{
    // 1.遍历系统app文件夹下全部应用，如果是自启动则启动
    // auto_start_app_by_path(PEDK_SYS_APP_WORKSPACE_PATH);
    // 2.遍历普通app文件夹下全部应用，如果是自启动则启动
    auto_start_app_by_path(PEDK_APP_WORKSPACE_PATH);
}

void AppManager::auto_start_app_by_path(std::string path)
{
    // 1.获取pedk_apps路径下，app列表
    std::vector<std::string> subdirectories = OS::get_directories(path);

    // 启动成功后，把app记到列表里管理起来
    AppManager* app_manager = AppManager::get_instance();

    // 2.逐一检查文件夹下的project.config.json文件
    std::vector<std::string>::iterator it;
    for (it = subdirectories.begin(); it != subdirectories.end(); ++it) {
        LOG_D("%s", it->c_str());
        // 2.1拼出APP路径
        std::string app_config_file_name = path + it->c_str() + PEDK_APP_CONFIG_FILENAME;

        LOG_D("%s", app_config_file_name.c_str());

        // 2.2 load配置文件
        std::string js_string = FileUtils::load_file(app_config_file_name);

        LOG_D("%s", js_string.c_str());
        if(js_string == ""){
            //文件打开失败，下一个
            continue;
        }

        // 2.3 如果"autoboot":true，则启动app
        bool auto_boot_flag = PedkJsonIO::get_autoboot(js_string);
        LOG_D("%d", auto_boot_flag);
        if (auto_boot_flag) {
            App app_info;

            app_info.m_app_name = *it;
            app_info.m_os_information.m_start_time = OS::get_systime();
            app_info.m_os_information.m_hearbeat_time = OS::get_systime();

            // 2.3.1 启动app
            OS::start_app(*it, app_info.m_os_information);
            // 2.3.2 添加到app列表中
            app_manager->add_app(app_info);
        }
    }
}

#include "cmd/cmds/app_cmds/app_back_cmd.h"
#include "pedk/pedk_transport/transport_pedk.h"
void AppManager::send_back_msg(std::string app_name)
{
    AppBackCmd app_back_cmd;
    std::string ss = app_back_cmd.serialize();
    TransportPedk *trans =TransportPedk::get_instance();

    for(const auto &pair :m_apps){
        if(pair.first != app_name){
            LOG_I("send backend to [%s]",pair.first);
            trans->send_to_app(pair.first, ss);
        }
    }
}

#include "cmd/cmds/app_cmds/app_front_cmd.h"
void AppManager::send_front_msg(std::string app_name)
{
    AppFrontCmd app_front_cmd;
    std::string ss = app_front_cmd.serialize();
    TransportPedk *trans =TransportPedk::get_instance();

    for(const auto &pair :m_apps){
        if(pair.first == app_name){
            LOG_I("send frontend to [%s]",pair.first);
            trans->send_to_app(app_name, ss);
        }
    }
}

void AppManager::check_pedk()
{  
    //现阶段pedk不对license进行时间校验 只对已安装的app数量
    // int64_t curr_time = (int64_t)OS::get_systime();

    // LOG_D("start_time:%d",m_installer_config.m_start_time);
    // LOG_D("end_time:%d",m_installer_config.m_end_time);
    // LOG_D("current_time:%d",curr_time);
    // if(m_installer_config.m_start_time <= curr_time 
    // && curr_time <= m_installer_config.m_end_time){
    //     m_pedk_enable = true;
    // }else{
    //     m_pedk_enable = false;
    // }

    uint32_t app_count = 0;
    app_count = OS::count_app_dir(PEDK_APP_WORKSPACE_PATH);
    if (app_count > 0)
    {
        m_pedk_enable = true;
    }
    else 
    {
        m_pedk_enable = false;
    }
}