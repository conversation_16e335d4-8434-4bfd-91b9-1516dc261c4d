#ifndef _APP_START_RES_CMD_H_
#define _APP_START_RES_CMD_H_

#include "cmd/cmds/app_cmds/app_cmds.h"
#include "cmd/exec/app_cmd_exec.h"

typedef struct AppStartResCmdFormat {
    uint32_t    type; // 指令类型
    std::string app_name;
    bool        res;
    MSGPACK_DEFINE(type,app_name,res);
} AppStartResCmdFormat;

class AppStartResCmd: public AppCmd{
public:
    AppStartResCmd();
    AppStartResCmd(std::string app_name, bool res);
    ~AppStartResCmd();
    uint32_t execute(AppCmdExec *app_cmd_exec) override;
    std::string serialize() override;
    uint32_t deserialize(std::string ss) override;
protected:
private:
    AppStartResCmdFormat m_app_start_res_cmd;
};

#endif // _APP_START_RES_CMD_H_