#include "app_uplink_data_cmd.h"

AppUplinkDataCmd::AppUplinkDataCmd()
{
    m_app_uplink_data_cmd.type = MSG_APP_UPLINK_DATA;
    m_app_uplink_data_cmd.target = "";
    m_app_uplink_data_cmd.msg = "";
}

AppUplinkDataCmd::AppUplinkDataCmd(std::string target, std::string msg)
{
    m_app_uplink_data_cmd.type = MSG_APP_UPLINK_DATA;
    m_app_uplink_data_cmd.target = target;
    m_app_uplink_data_cmd.msg = msg;
}

AppUplinkDataCmd::~AppUplinkDataCmd()
{
}

uint32_t AppUplinkDataCmd::execute(AppCmdExec *app_cmd_exec)
{
    app_cmd_exec->app_uplink_data(m_app_uplink_data_cmd.target,m_app_uplink_data_cmd.msg);

    return 0;
}

std::string AppUplinkDataCmd::serialize()
{
    std::stringstream ss;
    msgpack::pack(ss, m_app_uplink_data_cmd);
    return ss.str();
}

uint32_t AppUplinkDataCmd::deserialize(std::string ss)
{
    //解码
    msgpack::object_handle oh = msgpack::unpack(ss.c_str(),ss.size());
    msgpack::object obj = oh.get();

    //将解码数据放到内部结构体中
    obj.convert(m_app_uplink_data_cmd);

    LOG_I("Data deserialize");

    return 0;
}