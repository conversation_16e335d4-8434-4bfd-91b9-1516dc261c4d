/**
 * 本文件由PEDK API DSL 生成，请不要修改本文件
 */

import { isType } from "../common/utils.js";

import { BuzzerCtrl_Impt } from "../implement/index.js";

/**
 * Buzzer control class. (Whether the buzzer is supported depends on the actual device model)
 * @class BuzzerCtrl
 */
export class BuzzerCtrl {
  constructor() {
    this._instance = new BuzzerCtrl_Impt();
  }

  buzzerOn() {
    return this._instance.buzzerOn();
  }

  buzzerOff() {
    return this._instance.buzzerOff();
  }

  buzz(times) {
    if (!isType(times, "Array")) {
      return "EINVALIDPARAM";
    }
    return this._instance.buzz(times);
  }
}
