#include "end_res_cmd.h"

EndResCmd::EndResCmd()
{
    m_end_res_cmd.type = MSG_END_RES;
    m_end_res_cmd.app_name = "";
    m_end_res_cmd.result = false;
}

EndResCmd::EndResCmd(std::string app_name, bool res)
{
    m_end_res_cmd.type = MSG_END_RES;
    m_end_res_cmd.app_name = app_name;
    m_end_res_cmd.result = res;
}

EndResCmd::~EndResCmd()
{
}

uint32_t EndResCmd::execute(PedkCmdExec *pedk_cmd_exec)
{
    pedk_cmd_exec->end_res(m_end_res_cmd.app_name, m_end_res_cmd.result);

    return 0;
}

std::string EndResCmd::serialize()
{
    std::stringstream ss;
    msgpack::pack(ss, m_end_res_cmd);
    return ss.str();
}

uint32_t EndResCmd::deserialize(std::string ss)
{
    //解码
    msgpack::object_handle oh = msgpack::unpack(ss.c_str(),ss.size());
    msgpack::object obj = oh.get();

    //将解码数据放到内部结构体中
    obj.convert(m_end_res_cmd);

    return 0;
}
