#ifndef _LOG_H_
#define _LOG_H_

#include <stdio.h>
#include <time.h>
#include <iostream>
#include <sys/time.h>

#include <string>
#include "basic/config.h"

/* log类型 */
enum LogType {
    LOG_TERMINAL, // 全部LOG输出到终端
    LOG_FILE,     // 全部LOG输出到一个大文件
    LOG_FILE_RELEASE, // 全部LOG输出到各自文件中。pedk主进程，输出到工作目录。子进程输出到app目录，js log输出到app目录
};

/* log级别 */
enum LogLevel {
    PEDK_LOG_LEVEL_DEBUG,
    PEDK_LOG_LEVEL_INFO,
    PEDK_LOG_LEVEL_WARNING,
    PEDK_LOG_LEVEL_ERROR,
    PEDK_LOG_LEVEL_FATAL
};

/* log配置，从config.json文件中获取 */
typedef struct LogConfig{
    LogType m_log_type;   
    LogLevel m_log_level;
    uint32_t m_log_file_max_line;
}LogConfig;


typedef struct LogFile {
    uint32_t line_num; // log行号
    std::string log_path; // log路径
    std::ofstream log_ofstream; // log文件流
    int log_fd;                 // log文件描述符
} LogFile;

class Log {
public:
    virtual void log_out(LogLevel log_level, std::string tag, const char* format, ...) = 0;
    virtual void log_out2(LogLevel log_level, std::string tag, const char* format, ...) = 0;
    virtual void logo_out() = 0;

    LogConfig m_log_config;
protected:
    std::string format_string(const char* format, ...);
    std::string get_time();
    void check_line_and_rename(LogFile &log_file, std::string new_name, std::string old_name);
    std::string log_level_to_str(enum LogLevel);
    
private:
  
};

#define LOG_TIME_FORMAT "%H:%M:%S"

#endif // _LOG_H_