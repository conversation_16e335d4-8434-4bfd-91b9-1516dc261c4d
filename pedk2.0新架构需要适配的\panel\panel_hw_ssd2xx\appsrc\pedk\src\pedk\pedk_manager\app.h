#ifndef _APP_H_
#define _APP_H_

#include <iostream>
#include "basic/os/os_information.h"
#include "basic/config.h"




/* 用户app配置*/
typedef struct UsrAppConfig{
    std::string icon_path;
    std::string display_name;
    //待定
}UserAppConfig;


class App {
public:  
    App();

    std::string m_app_name;
    OsInformation m_os_information;

    bool is_system_app;

    void update_heartbeat();

    std::string get_start_time_by_string();
    void kill_self();
protected:
private:
    bool m_heartbeat;
};

#endif // _APP_MANAGER_H_
