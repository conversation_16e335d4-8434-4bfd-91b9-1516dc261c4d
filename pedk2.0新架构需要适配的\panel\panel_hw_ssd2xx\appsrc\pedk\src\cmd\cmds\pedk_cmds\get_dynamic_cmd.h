#ifndef _GET_DYNAMIC_CMD_H_
#define _GET_DYNAMIC_CMD_H_

#include "cmd/cmds/pedk_cmds/pedk_cmds.h"
#include "cmd/exec/pedk_cmd_exec.h"

typedef struct GetDynamicCmdFormat {
    uint32_t type; // 指令类型

    MSGPACK_DEFINE(type);
} GetDynamicCmdFormat;

class GetDynamicCmd : public PedkCmd {
public:
    GetDynamicCmd();
    ~GetDynamicCmd();
    uint32_t execute(PedkCmdExec *pedk_cmd_exec) override;
    std::string serialize() override;
    uint32_t deserialize(std::string ss) override;
protected:
private:
    GetDynamicCmdFormat m_get_dynamic_cmd;
};

#endif // _GET_DYNAMIC_CMD_H_