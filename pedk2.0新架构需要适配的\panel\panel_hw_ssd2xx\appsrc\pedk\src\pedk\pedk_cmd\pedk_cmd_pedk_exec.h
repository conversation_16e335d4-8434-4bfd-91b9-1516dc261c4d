#ifndef _PEDK_CMD_PEDK_EXEC_H_
#define _PEDK_CMD_PEDK_EXEC_H_

#include "cmd/exec/pedk_cmd_exec.h"

class PedkCmdPedkExec: public PedkCmdExec{
public:
    ~PedkCmdPedkExec();

    void ping() override;
    void start(std::string app_name) override;
    void end(std::string app_name) override;
    void uplink_data(std::string target_app_name, std::string msg) override;
    void get_static_property(std::string app_name) override;
    void get_dynamic_property() override;
    void get_app_info() override;
    void error_info(std::string err_type, std::string err_info) override;

    static PedkCmdPedkExec* get_instance(); 
protected:
private:
    PedkCmdPedkExec();

    static PedkCmdPedkExec *m_instance;

    bool checkAppRootInfo(std::string app_name);
};

#endif // _PEDK_CMD_PEDK_EXEC_H_