#include "os.h"
#include <unistd.h>
#include <sys/wait.h>
#include <string>
#include <cstring>
#include <iostream>
#include "basic/config.h"
#include <stdio.h>
#include <vector>
#include <string>
#include <set>
#include "basic/os/os_information.h"
#include <dirent.h>
#include <cstdlib>
#include <sys/stat.h>
#include <sys/types.h>
#include <fcntl.h>
#include <fstream>
#include <sys/file.h>

std::set<std::string> filesToDelete = {
    "lost+found",
    "SysUI"
};


/**
 * @brief 启动pedk_app进程，并且把应用名作为参数传给pedk_app进程
 *
 * @param app_name
 * @param os_info
 * @return true
 * @return false
 */
bool OS::start_app(std::string app_name, OsInformation &os_info)
{
    pid_t pid;
    int ret;

    /* 获取pedk_app.elf可执行文件存放路径 */
    std::string pedk_program = PEDK_APP_EXEC_FILE;

    char *program_cstr = const_cast<char *>(pedk_program.c_str());
    char *arg1_cstr = const_cast<char *>(app_name.c_str());

    std::vector<char *> args = {program_cstr, arg1_cstr, nullptr};

    /* 创建子进程 */
    pid = fork();
    if (pid < 0)
    {
        LOG_E("start app fail, fork fail!");
        return false;
    }
    else if (pid == 0)
    {
        // 子进程被pedk_app进程替换，并把app名作为参数传递。
        ret = execv(program_cstr, args.data());
    }
    else
    {
        // wait(NULL);
        // 保存子进程的pid信息，方便在结束时强制杀死
        os_info.m_pid = pid;
    }

    return true;
}

/**
 * @brief 获取系统时间
 *
 * @return time_t
 */
time_t OS::get_systime()
{
    return time(NULL);
}

/**
 * @brief time_t类型转化成string类型
 *
 * @param tm
 * @return std::string
 */
std::string OS::time_to_string(time_t *tm)
{
    std::string ret;
    char *c_time_string;

    c_time_string = ctime(tm);
    ret = c_time_string;

    return ret;
}

/**
 * @brief 等待并监视app退出
 *        根据app信息的os_info中存储的pid监视进程。
 *        最大等待退出时间由END_WAIT_TIME配置。
 * @param os_info
 * @return true 监视时间内自动退出
 * @return false 监视时间内没有自动退出
 */
bool OS::wait_app_exit(OsInformation os_info)
{
    int status;
    bool ret = false;

    pid_t pid = os_info.m_pid;
    std::cout << "pid =" << pid << std::endl;
    for (int i = 0; i < END_WAIT_TIME; i++)
    {
        if (0 != waitpid(pid, &status, WNOHANG))
        {
            ret = true;
            break;
        }
        std::cout << "sleep" << std::endl;
        sleep(1);
    }

    return ret;
}

/**
 * @brief 强制杀死pedk_app进程
 *
 * @param os_info
 */
void OS::kill_app(OsInformation os_info)
{
    int status;
    pid_t pid = os_info.m_pid;

    if (0 == waitpid(pid, &status, WNOHANG))
    {
        kill(pid, SIGKILL);
    }
}

/**
 * @brief 获取文件夹下所有子文件夹名称
 *
 * @param folderPath 指定文件夹路径
 * @return std::vector<std::string>  文件夹下全部子文件夹名称。
 */
std::vector<std::string> OS::get_directories(std::string folderPath)
{
    std::vector<std::string> subdirectories;

    DIR *dir = opendir(folderPath.c_str());
    if (dir)
    {
        struct dirent *entry;
        while ((entry = readdir(dir)) != nullptr)
        {
            if (entry->d_type == DT_DIR && std::string(entry->d_name) != "." && std::string(entry->d_name) != "..")
            {
                subdirectories.push_back(entry->d_name);
            }
        }
        closedir(dir);
    }
    else
    {
        LOG_E("Error opening directory:%s", folderPath.c_str());
    }

    return subdirectories;
}

/**
 * @brief 获取文件夹下所有文件名称
 *
 * @param folderPath 指定文件夹路径
 * @return std::vector<std::string>  文件夹下全部子文件名称。
 */
std::vector<std::string> OS::get_data_file(std::string folderPath)
{
    std::vector<std::string> subdirectories;

    DIR *dir = opendir(folderPath.c_str());
    if (dir)
    {
        struct dirent *entry;
        while ((entry = readdir(dir)) != nullptr)
        {
            if (entry->d_type == DT_REG && std::string(entry->d_name) != "." && std::string(entry->d_name) != "..")
            {
                subdirectories.push_back(entry->d_name);
            }
        }
        closedir(dir);
    }
    else
    {
        LOG_E("Error opening directory:%s", folderPath.c_str());
    }

    return subdirectories;
}

/**
 * @brief 复制文件夹
 *        使用系统命令cp -r 复制文件夹及子文件夹下全部内容
 * @param target_path
 * @param src_folder
 */
void copyFile(const std::string &destination, const std::string &source)
{
    std::ifstream src(source, std::ios::binary);
    std::ofstream dst(destination, std::ios::binary);
    dst << src.rdbuf(); // 直接读取整个文件并写入到目标文件
}

void OS::copy_app(const std::string &destDir, const std::string &sourceDir)
{
    // 创建目标目录
    mkdir(destDir.c_str(), 0777); // 0755 是目录的权限

    // 打开源目录
    DIR *dir = opendir(sourceDir.c_str());
    std::cout << "cp " << sourceDir << " to " << destDir << std::endl;

    if (dir == nullptr)
    {
        perror("opendir failed");
        return;
    }

    struct dirent *entry;
    while ((entry = readdir(dir)) != nullptr)
    {
        // 跳过 . 和 .. 目录
        if (std::string(entry->d_name) == "." || std::string(entry->d_name) == "..")
        {
            continue;
        }

        std::string sourcePath = sourceDir + "/" + entry->d_name;
        std::string destPath = destDir + "/" + entry->d_name;

        if (entry->d_type == DT_DIR)
        {
            // 如果是目录，递归调用
            copy_app(destPath, sourcePath);
        }
        else if (entry->d_type == DT_REG)
        {
            // 如果是普通文件，调用 copyFile
            copyFile(destPath, sourcePath);
        }
    }

    closedir(dir);
}

/**
 * @brief 休眠
 *
 */
void OS::pedk_sleep(unsigned int sleep_second)
{
    sleep(sleep_second);
}

/**
 * @brief 强制自退出
 *
 */
void OS::app_exit()
{
    exit(0);
}

/**
 * @brief 检查app文件夹下是否有data文件夹，没有就创建一个
 *
 */
void OS::check_data_dir_and_make(const std::string &app_path)
{
    // 检查data文件夹是否存在
    std::string data_path = app_path + "/data";

    struct stat info;
    bool data_exit = false;
    if (stat(data_path.c_str(), &info) == 0)
    {
        // 存在data文件
        if (info.st_mode & S_IFDIR)
        {
            // 并且data是文件夹
            data_exit = true;
        }
    }

    // 创建data文件夹
    if (false == data_exit)
    {
        if (0 != mkdir(data_path.c_str(), 0755))
        {
            LOG_E("make data dir fail");
        }
    }
}

int OS::file_open(std::string file_path)
{
    int fd;

    fd = open(file_path.c_str(), O_RDWR | O_CREAT, 0777);
    if (fd == -1)
    {
        std::cerr << "Error open file:" << strerror(errno) << std::endl;
        return -1;
    }

    return fd;
}

int OS::file_lock(int fd)
{
    int ret;

    ret = flock(fd, LOCK_EX);
    if (ret == -1)
    {
        std::cerr << "Error acquiring write lock:" << strerror(errno) << std::endl;
        return -1;
    }

    return ret;
}

int OS::file_unlock(int fd)
{
    int ret;

    ret = flock(fd, LOCK_UN);
    if (ret == -1)
    {
        std::cerr << "Error acquiring write lock:" << strerror(errno) << std::endl;
        return -1;
    }

    return ret;
}

/**
 * @brief 统计普通app文件夹数量
 *
 * @param folderPath 普通app存放路径
 * @return uint32_t app数量
 */
uint32_t OS::count_app_dir(const std::string &folderPath)
{
    uint32_t app_dir_count = 0;
    DIR *dir = opendir(folderPath.c_str());
    if (dir)
    {
        struct dirent *entry;
        while ((entry = readdir(dir)) != nullptr)
        {
            if (entry->d_type == DT_DIR && std::string(entry->d_name) == "." || entry->d_type == DT_DIR && std::string(entry->d_name) == "..")
            {
                continue;
            }

            if (entry->d_type == DT_DIR)
            {
                std::string file_name = entry->d_name;
                if (filesToDelete.find(file_name) != filesToDelete.end())
                {
                    char command[1024];
                    std::snprintf(command, sizeof(command), "rm -rf %s%s", folderPath.c_str(), entry->d_name);
                    std::cout << "the command is  " << command<< std::endl;
                    uint32_t result = std::system(command);
                    if (result == 0)
                    {
                        std::cout << "deleted unuseful file successfully" << std::endl;
                    }
                    else
                    {
                        std::cout << " Failed to delete directory" << std::endl;
                    }
                }
                else
                {
                    app_dir_count++;
                }
            }
        }
        closedir(dir);
    }

    else
    {
        std::cout << "Error opening directory pedk work space failed" << std::endl;
    }
    return app_dir_count;
}


