#include "pedk_cmd_pedk_exec.h"
#include "pedk/pedk_manager/app.h"
#include "pedk/pedk_manager/app_manager.h"
#include "basic/os/os.h"
#include "basic/os/os_information.h"
#include "basic/utils/file_utils.h"
#include "pedk/pedk_transport/transport_pedk.h"
#include "pedk/pedk_cmd/pedk_cmd_context.h"
#include "pedk/pedk_basic/pedk_json_io.h"
#include "cmd/cmds/app_cmds/app_system_error_cmd.h"
#include <iostream>
#include "basic/config.h"
#include "cmd/cmds/cmd.h"
#include "basic/json_io/json_io.h"


PedkCmdPedkExec* PedkCmdPedkExec::m_instance = nullptr;

PedkCmdPedkExec::PedkCmdPedkExec()
{
}
    
PedkCmdPedkExec::~PedkCmdPedkExec()
{
}

PedkCmdPedkExec* PedkCmdPedkExec::get_instance()
{
    if(m_instance == nullptr){
        m_instance = new PedkCmdPedkExec;
    }

    return m_instance;
}

/**
 * @brief ping消息处理函数
 *        收到ping消息后、代表打印机的进程已经准备OK，可以启动自启动APP了。
 */
#include "cmd/cmds/pedk_cmds/ping_res_cmd.h"
void PedkCmdPedkExec::ping()
{
    //1.取出pedk使能结果
    AppManager* app_manager = AppManager::get_instance();

    //2.从installer配置文件中获取有效时间
    app_manager->m_installer_config = JsonIO::get_installer_config(PEDK_INSTALLER_CONFIG_PATH);
    //3. 检查pedk证书是否失效
    app_manager->check_pedk();

    //4.返回ping_res消息
    PingResCmd ping_res_cmd(app_manager->m_pedk_enable);
    std::string ss = ping_res_cmd.serialize();
    
    PedkCmdContext::PEDKMsg(MSG_PING_RES);
    LOG_I("PEDK licence : %d",app_manager->m_pedk_enable);

    TransportPedk *trans = TransportPedk::get_instance();
    trans->send(ss);

    //5.如果使能结果为false，则1秒后关闭pedk主进程,停留1秒是为了保证ping_res消息能顺利发出去。
    if(false == app_manager->m_pedk_enable){
        OS::pedk_sleep(1);
        LOG_F("PEDK not enabled, cannot be used!");
        OS::app_exit();
    }
}

/**
 * @brief start消息处理函数
 *        (1)会存储启动时间
 *        (2)存储初始心跳时间
 *        (3)启动app子进程
 *        (4)记录到app管理中
 * @param app_name 
 */
#include "cmd/cmds/pedk_cmds/start_res_cmd.h"
void PedkCmdPedkExec::start(std::string app_name)
{
    LOG_I("Start APP [%s]",app_name.c_str());    

    //1.先检查该APP是否已经启动
    AppManager *app_manager = AppManager::get_instance();
    App app;
    bool is_find = app_manager->find_app(app_name,&app);

    //2. 如果没启动，则启动。
    if(!is_find){
        // 2.1拼出APP路径
        std::string app_config_file_name = PEDK_APP_WORKSPACE_PATH;
        app_config_file_name += app_name;
        app_config_file_name += PEDK_APP_CONFIG_FILENAME;

        // 2.2 load配置文件
        bool time_is_ok = false;
        std::string js_string = FileUtils::load_file(app_config_file_name);
        if(js_string != ""){
            //获取当前系统时间、有效起始时间，有效结束时间。
            int64_t currtime = (int64_t)OS::get_systime();
            int64_t start_time = PedkJsonIO::get_starttime(js_string);
            int64_t end_time = PedkJsonIO::get_endtime(js_string);
            LOG_D("start_time:%d",start_time);
            LOG_D("end_time:%d",end_time);
            LOG_D("currtime:%d",currtime);

            //2.2.1判断是否在有效期内
            if(start_time <= currtime && currtime <= end_time){
                time_is_ok = true;
            }
        }else{
            LOG_E("{%s} don't have project.config.json file.",app_name.c_str());
        }   

        //2.3 判断是否在有效期内
        if(time_is_ok == true){
            //****在有效期内
            //2.3.1 如果没有启动，则先向除此app之外的app发送切换到后台指令
            app_manager->send_back_msg(app_name);
            
            //2.3.2.再启动此APP
            App app_info;
            //启动pedk_app进程，然后把名字传过去
            app_info.m_app_name = app_name;
            app_info.m_os_information.m_start_time = OS::get_systime();
            app_info.m_os_information.m_hearbeat_time = OS::get_systime();

            OS::start_app(app_name,app_info.m_os_information);

            //启动成功后，把app记到列表里管理起来
            app_manager->add_app(app_info);
        }else{
            //****不在有效期内
            ///2.3.3 先返回个启动失败结果
            StartResCmd start_res_cmd(app_name,false);
            std::string ss = start_res_cmd.serialize();
            TransportPedk *trans = TransportPedk::get_instance();
            trans->send(ss);

            //2.3.4 再将错误消息广播给全部app
            LOG_E("[%s] in valid time!!",app_name.c_str());
            //向系统UI APP发送异常指令
            AppSystemErrorCmd app_system_error_cmd("start_fail",app_name);
            ss = app_system_error_cmd.serialize();

            PedkCmdContext::PEDKMsg(MSG_ERROR_INFO);

            trans = TransportPedk::get_instance();
            trans->send_to_app("Broadcast",ss);
        }
    }else{
     //3. 如果启动了，则调用前后台切换。
        //3.2.如果已经启动了，则先向除此APP之外的app，发送“切换到后台”程序       
        PedkCmdContext::PEDKMsg(MSG_APP_BACK);
        app_manager->send_back_msg(app_name);

        //3.3.再向此APP发送“切换到前台”消息
        
        StartResCmd start_res_cmd(app_name,true);
        std::string ss = start_res_cmd.serialize();
        TransportPedk *trans = TransportPedk::get_instance();
        trans->send(ss);

        PedkCmdContext::PEDKMsg(MSG_APP_FRONT);
        app_manager->send_front_msg(app_name);

    }
}

/**
 * @brief end消息处理函数
 *        (1)给app发送结束消息
 *        (2)监视app进程 END_WAIT_TIME 秒
 *        (3)如果END_WAIT_TIME 秒内没退出app进程，则强制杀死
 *        (4)返回end_res消息
 * @param app_name 
 */
#include "cmd/cmds/app_cmds/app_end_cmd.h"
#include "cmd/cmds/pedk_cmds/end_res_cmd.h"
#include "cmd/cmds/pedk_cmds/start_res_cmd.h"
void PedkCmdPedkExec::end(std::string  app_name)
{
    LOG_I("End APP [%s]",app_name.c_str());

     //先检查app是否存
    AppManager* app_manager = AppManager::get_instance();
    App app;
    bool result = app_manager->find_app(app_name, &app);

    if(result){
        TransportPedk *trans = TransportPedk::get_instance();
        //1.结束指定app
        PedkCmdContext::PEDKMsg(MSG_APP_END);
        AppEndCmd app_end_cmd;
        std::string ss = app_end_cmd.serialize();
        trans->send_to_app(app_name, ss);

        //2.监视app进程结束
        LOG_D("wait app exit");
        if(!OS::wait_app_exit(app.m_os_information)){
            LOG_E("kill app");
            OS::kill_app(app.m_os_information);
        }

        //3.从app管理中删除
        app_manager->delete_app(app_name);

        //4.返回end_res成功指令
        PedkCmdContext::PEDKMsg(MSG_END_RES);
        LOG_I("Successfully close app[%s]", app_name.c_str());
        EndResCmd end_res_cmd(app_name, true);
        std::string ess = end_res_cmd.serialize();
        trans->send(ess);
    }else{
        //4.返回end_res错误指令
        PedkCmdContext::PEDKMsg(MSG_END_RES);
        LOG_I("No app found named by [%s]", app_name.c_str());
        TransportPedk *trans = TransportPedk::get_instance();
        EndResCmd end_res_cmd(app_name, false);
        std::string ess = end_res_cmd.serialize();
        trans->send(ess);   
    }

}

/**
 * @brief uplink_data消息处理函数
 *        透传转发消息给指定app
 * @param target_app_name 
 * @param msg 
 */
#include "cmd/cmds/app_cmds/app_downlink_data_cmd.h"
void PedkCmdPedkExec::uplink_data(std::string target_app_name, std::string msg)
{
    TransportPedk *trans = TransportPedk::get_instance();
    AppDownlinkDataCmd *app_downlink_data_cmd = new AppDownlinkDataCmd(msg);
    
    std::string ss = app_downlink_data_cmd->serialize();

    PedkCmdContext::PEDKMsg(MSG_APP_DOWNLINK_DATA);
    LOG_I("Transmitting msg to [%s]",target_app_name.c_str());

    trans->send_to_app(target_app_name, ss);

    delete app_downlink_data_cmd;
}

#include "cmd/cmds/pedk_cmds/get_static_cmd.h"
#include "cmd/cmds/pedk_cmds/ret_static_cmd.h"
#include "basic/utils/file_utils.h"
/**
 * @brief 获取app静态属性
 *        不与app进程交互，无论app是否启动，都可以获取静态属性。
 *        静态属性在app文件夹下的app.json文件中存储。
 *        打开、获取、然后转发给客户端。
 * @param app_name 
 * @param msg 
 */
void PedkCmdPedkExec::get_static_property(std::string app_name)
{
    //1.直接拼app静态属性文件完整路径
    std::string app_json_path = PEDK_APP_WORKSPACE_PATH + app_name + PEDK_APP_STATIC_PROPERTY_FILENAME;

    //2.打开获取文件全部内容
    std::string static_msg = FileUtils::load_file(app_json_path);

    //3.制作ret_static_property消息，将静态属性透传回去。
    RetStaticCmd ret_static_cmd(app_name,static_msg);
    std::string ss = ret_static_cmd.serialize();

    PedkCmdContext::PEDKMsg(MSG_RET_STATIC_PROPERTY);
    LOG_I("[%s]'s app.json\n%s",app_name.c_str(), static_msg.c_str());

    TransportPedk *trans = TransportPedk::get_instance();
    trans->send(ss);
}

#include "cmd/cmds/pedk_cmds/ret_dynamic_cmd.h"
#include <map>
/**
 * @brief 获取全部app动态属性
 *        仅可获取已打开app的动态属性。
 *        动态属性全部在AppManager中管理保存
 *        不需要与app进程通信，直接打包返回给客户端。
 */
void PedkCmdPedkExec::get_dynamic_property()
{
    // 获取APP管理实例
    AppManager* app_manager = AppManager::get_instance();

    std::map<std::string, App>& apps = app_manager->get_apps();

    PedkCmdContext::PEDKMsg(MSG_RET_DYNAMIC_PROPERTY);

    RetDynamicCmdFormat  ret_dynamic_cmd_format;
    for (const auto& it : apps) {
        App app = it.second;
        DynamicAttr app_attr;
        app_attr.app_name = app.m_app_name;
        app_attr.start_time = app.get_start_time_by_string();
        LOG_I("app_name:%s",app_attr.app_name.c_str());
        LOG_I("start_time:%s",app_attr.start_time.c_str());
        ret_dynamic_cmd_format.apps_dynamic_arrt.push_back(app_attr);
    }
    
    // 将实例传给，RetDynamicCmd消息类
    RetDynamicCmd ret_dynamic_cmd(ret_dynamic_cmd_format);

    // 打包数据
    std::string ss;
    ss = ret_dynamic_cmd.serialize();

    // 获取传输层实例，发送数据
    TransportPedk* trans = TransportPedk::get_instance();
    trans->send(ss);
}

//制作icon路径
std::string make_icon_path(std::string app_name , const std::string& icon_path)
{
    if (icon_path.empty())
    {
        LOG_I("user icon path is not exist");
        return "";
    }
    std::string complete_icon_path;

    complete_icon_path = PEDK_APP_WORKSPACE_PATH;
    complete_icon_path += app_name;
    complete_icon_path += icon_path;

    if(FileUtils::file_exist(complete_icon_path)){
        return complete_icon_path;
    }

    return "";
}

#include "cmd/cmds/pedk_cmds/ret_app_info_cmd.h"
#include "pedk/pedk_basic/pedk_json_io.h"
bool PedkCmdPedkExec::checkAppRootInfo(std::string app_name)
{
    //1.如果是空，直接返回false
    if(app_name == ""){
        return false;
    }
    
    //2.如果非空，去拿此APP下的project.config.json文件内容。
    //2.1拼出APP路径
    std::string app_config_file_name = PEDK_APP_WORKSPACE_PATH;
    app_config_file_name += app_name;
    app_config_file_name += PEDK_APP_CONFIG_FILENAME;
    std::string js_string = FileUtils::load_file(app_config_file_name);

    //2.2如果打开配置文件失败,返回失败
    if(js_string == ""){
        return false;
    }

    //2.3如果打开配置文件成功，则提取信息
    ProjectConfig project_config = JsonIO::get_project_config_json(js_string);
    if(project_config.root_app == true){
        return true;
    }else{
        return false;
    }
}

void PedkCmdPedkExec::get_app_info()
{
    // 1.获取root app信息
    RetAppInfoCmdFormat  ret_app_info_format;
    AppManager* app_manager = AppManager::get_instance();
    
    /*bug:137770,给与root app名称前，要判断两个条件
        (1)installer_config.json文件中有app_name
        (2)并且此app_name指定的APP，的project.config.json文件中的root_app也是true。
        (3)否则不赋值这个name
    */
    if(checkAppRootInfo(app_manager->m_installer_config.m_root_app)){
        ret_app_info_format.root_app_name = app_manager->m_installer_config.m_root_app;
    }else{
        ret_app_info_format.root_app_name = "";
    }
    

    // 2.获取apps_info信息
    std::vector<std::string> subdirectories = OS::get_directories(PEDK_APP_WORKSPACE_PATH);    // 获取pedk_apps路径下，app列表
    // 2.1.逐一检查文件夹下的project.config.json文件,根据信息判断是否自启动；是否显示图标；以及获取图标路径
    std::vector<std::string>::iterator it;
    for (it = subdirectories.begin(); it != subdirectories.end(); ++it) {
         // 2.1.1拼出APP路径
        std::string app_config_file_name = PEDK_APP_WORKSPACE_PATH;
        app_config_file_name += it->c_str();
        app_config_file_name += PEDK_APP_CONFIG_FILENAME;

        // 2.1.2 load配置文件
        std::string js_string = FileUtils::load_file(app_config_file_name);

        // 2.1.3如果打开配置文件失败，直接下一个
        if(js_string == ""){
            continue;
        }


        //1.直接拼app静态属性文件完整路径
        std::string user_app_json_path;
        user_app_json_path += PEDK_APP_WORKSPACE_PATH;
        user_app_json_path += it->c_str();
        user_app_json_path += PEDK_APP_STATIC_PROPERTY_FILENAME;

            //2.打开获取文件全部内容
        std::string app_json_content = FileUtils::load_file(user_app_json_path);
        if (js_string == "")
        {
            continue;
        }
            
            //解析icon路径配置文件信息app.json
         UserAppConfig usr_app_config = PedkJsonIO::get_user_app_config_json(app_json_content);


        //LOG_D("<%s>",it->c_str());
        // 2.1.4 解析app的配置文件信息project.config.json,根据app的配置信息和root app信息，综合判断app info信息
        ProjectConfig project_config = JsonIO::get_project_config_json(js_string);

        //****根据规格说明书 3.18.3章节设计的判断原则实现以下内容***//
        AppInfo app_info;
        if(project_config.root_app == true && app_manager->m_installer_config.m_root_app == it->c_str()){
            //情况1：当配置信息root_app为真，并且总配置文件指定的rootapp就是当前app，则认为是当前有效的root app
            app_info.app_name = it->c_str();
            app_info.is_autoboot = true;
            app_info.display_icon = false;  //默认root app没有icon
            app_info.icon_path = "";        //默认root app没有icon
            app_info.display_name = usr_app_config.display_name;

            ret_app_info_format.apps_info.push_back(app_info); //只有root app插在最后
        }
        /*else if(project_config.root_app == true && app_manager->m_installer_config.m_root_app != it->c_str()){
            //情况2：当配置信息root_app为真，但是总配置文件指定的rootapp不是当前app，则认为是当前无效的旧root app
        }*/
        else if(project_config.root_app == false ){
            //情况3、4：当配置信息root_app为假，自启动为真，则认为是用户特意设计的后台自启动app
            //情况5、6：当配置信息root_app为假，自启动为假，则认为是普通app




            app_info.app_name = it->c_str();
            app_info.is_autoboot = project_config.autoboot;      //是否自启动根据用户配置
            app_info.display_icon = project_config.icon_display; //是否有图标根据用户配置
            app_info.icon_path = "";        //配置icon路径
            app_info.display_name = usr_app_config.display_name;

            if(app_info.display_icon == true){
                //制作icon路径
                app_info.icon_path = make_icon_path(it->c_str(), usr_app_config.icon_path);
            }

            ret_app_info_format.apps_info.insert(ret_app_info_format.apps_info.begin(),app_info); //插到队列前端
        }
    }

    //2.ret_app_info_format
    RetAppInfoCmd ret_app_info_cmd(ret_app_info_format);
    std::string ss = ret_app_info_cmd.serialize();
    
    PedkCmdContext::PEDKMsg(MSG_RET_APP_INFO);
    LOG_I("root_app_name:%s\n",ret_app_info_format.root_app_name.c_str());
    for(const auto& app : ret_app_info_format.apps_info){
        LOG_I(",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,");
        LOG_I("app_name:<%s>",app.app_name.c_str());
        LOG_I("display_icon:%d",app.display_icon);
        LOG_I("icon_path:%s",app.icon_path.c_str());
        LOG_I("display_name: %s", app.display_name.c_str());
        LOG_I("is_autoboot:%d",app.is_autoboot);
    }

    TransportPedk *trans = TransportPedk::get_instance();
    trans->send(ss);
}

/**
 * @brief 错误信息上报
 * 
 * @param err_type 预留，可能会有扩展
 * @param err_info 
 */
#include "cmd/cmds/app_cmds/app_system_error_cmd.h"
void PedkCmdPedkExec::error_info(std::string err_type, std::string err_info)
{
    //将错误消息广播给全部app
  
    //向系统UI APP发送异常指令
    AppSystemErrorCmd app_system_error_cmd("sys_error",err_info);
    std::string ss = app_system_error_cmd.serialize();
    
    PedkCmdContext::PEDKMsg(MSG_APP_SYS_ERR);
    LOG_E("err_info:[%s]",err_info.c_str());

    TransportPedk *trans = TransportPedk::get_instance();
    trans->send_to_app("Broadcast",ss);
}



