#include "app_cmd_pedk_exec.h"
#include "pedk/pedk_transport/transport_pedk.h"
#include "cmd/cmds/cmd.h"
#include "session_generator.h"
#include "cmd/cmds/app_cmds/ret_session_cmd.h"
AppCmdPedkExec* AppCmdPedkExec::m_instance = nullptr;

AppCmdPedkExec::AppCmdPedkExec()
{
}
    
AppCmdPedkExec::~AppCmdPedkExec()
{
}

AppCmdPedkExec* AppCmdPedkExec:: get_instance()
{
    if(m_instance == nullptr){
        m_instance = new AppCmdPedkExec;
    }

    return m_instance;
}

/**
 * @brief 收到pedk_app进程的启动响应消息，然后将结果转给客户端进程
 * 
 * @param app_name 
 * @param res 
 */
#include "cmd/cmds/pedk_cmds/start_res_cmd.h"
void AppCmdPedkExec::app_start_res(std::string app_name, bool res)
{
    if(res){
        LOG_I("start %s SUCCESS",app_name.c_str());
    }else{
        LOG_E("start %s FAIL",app_name.c_str());
    }

    StartResCmd start_res_cmd(app_name,res);
    std::string ss = start_res_cmd.serialize();

    TransportPedk *trans = TransportPedk::get_instance();
    trans->send(ss);
}

/**
 * @brief app的心跳消息
 *       收到app的心跳消息后，到app管理中，更新一下app的心跳时间
 * 
 * @param app_name 
 */
#include "pedk/pedk_manager/app_manager.h"
void AppCmdPedkExec::app_heart_beat(std::string app_name)
{
    //获取APP管理
    //LOG_D("app_heart_beat");
    AppManager *app_manager = AppManager::get_instance();
    app_manager->update_heartbeat(app_name);
}

/**
 * @brief 收到app上行数据，然后转发
 *        上行数据分两种：、
 *        （1）一种是发送给客户端，需要使用DownlinkDataCmd指令转发
 *        （2）一种是发送给其他APP或者广播，需要使用AppDownlinkDataCmd指令转发。
 * @param target 
 * @param msg 
 */
#include "pedk/pedk_transport/transport_pedk.h"
#include "cmd/cmds/pedk_cmds/downlink_data_cmd.h"
#include "cmd/cmds/app_cmds/app_downlink_data_cmd.h"
void AppCmdPedkExec::app_uplink_data(std::string target, std::string msg)
{
    if(target == CLIENT_NAME){
        //转发
        DownlinkDataCmd *downlink_data_cmd =  new DownlinkDataCmd(msg);
        std::string ss = downlink_data_cmd->serialize();

        TransportPedk *trans = TransportPedk::get_instance();
        LOG_I("MSG_DOWNLINK_DATA ->");
        trans->send_to_app(target, ss);
    }else {
        //转发
        AppDownlinkDataCmd *app_downlink_data_cmd =  new AppDownlinkDataCmd(msg);
        std::string ss = app_downlink_data_cmd->serialize();

        TransportPedk *trans = TransportPedk::get_instance();
        LOG_I("MSG_APP_DOWNLINK_DATA ->");
        trans->send_to_app(target, ss);
    }
}

#include "cmd/cmds/app_cmds/app_end_cmd.h"
#include "cmd/cmds/pedk_cmds/end_res_cmd.h"
#include "basic/os/os.h"
void AppCmdPedkExec::app_exit(std::string app_name)
{
    //1.先检查app是否存
    AppManager* app_manager = AppManager::get_instance();
    App app;
    bool result = app_manager->find_app(app_name, &app);

    if(result){
        // 2. 如果存在
        TransportPedk *trans = TransportPedk::get_instance();
        
        // 2.1. 结束指定app
        AppEndCmd app_end_cmd;
        std::string ss = app_end_cmd.serialize();
        LOG_I("MSG_APP_END ->");
        trans->send_to_app(app_name, ss);

        // 2.2. 结束指定app
        LOG_D("wait app exit"); 
        if(!OS::wait_app_exit(app.m_os_information)){
            LOG_E("kill app");
            OS::kill_app(app.m_os_information);
        }

        //2.3.从app管理中删除
        app_manager->delete_app(app_name);

        //2.4 再给客户端发送一个结束响应消息
        EndResCmd *end_res_cmd =  new EndResCmd(app_name, true);
        ss = end_res_cmd->serialize();

        LOG_I("MSG_END_RES ->");
        trans->send(ss);
    }

}

void AppCmdPedkExec::app_reset(std::string app_name)
{
    //1.先检查app是否存
    AppManager* app_manager = AppManager::get_instance();
    App app;
    bool result = app_manager->find_app(app_name, &app);

    if(result){
        // 2. 如果存在，执行退出流程
        TransportPedk *trans = TransportPedk::get_instance();
        
        // 2.1. 结束指定app
        AppEndCmd app_end_cmd;
        std::string ss = app_end_cmd.serialize();
        LOG_I("MSG_APP_END ->");
        trans->send_to_app(app_name, ss);

        // 2.2. 结束指定app
        LOG_D("wait app exit"); 
        if(!OS::wait_app_exit(app.m_os_information)){
            LOG_E("kill app");
            OS::kill_app(app.m_os_information);
        }

        //2.3.从app管理中删除
        app_manager->delete_app(app_name);


        //3. 如果存在，再执行启动流程
        //3.1. 如果没有启动，则先向除此app之外的app发送切换到后台指令
        app_manager->send_back_msg(app_name);
        
        //3.2. 启动pedk_app进程，然后把名字传过去
        App app_info;
        app_info.m_app_name = app_name;
        app_info.m_os_information.m_start_time = OS::get_systime();
        app_info.m_os_information.m_hearbeat_time = OS::get_systime();
        OS::start_app(app_name,app_info.m_os_information);

        //3.3. 启动成功后，把app记到列表里管理起来
        app_manager->add_app(app_info);
    }
}

void AppCmdPedkExec::app_close_spec_app(std::string app_name)
{
    //去关闭指定app
    LOG_I("close spec APP [%s]",app_name.c_str());

    //1. 先检查app是否存
    AppManager* app_manager = AppManager::get_instance();
    App app;
    bool result = app_manager->find_app(app_name, &app);

    if(result){
        //2.强制关闭app进程
        LOG_E("kill app");
        OS::kill_app(app.m_os_information);

        //3.从app管理中删除
        app_manager->delete_app(app_name);

        //4  再给客户端发送一个结束响应消息
        EndResCmd *end_res_cmd =  new EndResCmd(app_name, true);
        std::string ss = end_res_cmd->serialize();

        TransportPedk *trans = TransportPedk::get_instance();
        LOG_I("MSG_END_RES ->");
        trans->send(ss);

    }
}

#include "cmd/cmds/pedk_cmds/reset_printer_cmd.h"
void AppCmdPedkExec::app_reset_printer()
{
    //发一条重启消息给打印机，打印机去重启。
    LOG_I("reset printer!!!!!:");

    ResetPrinterCmd *reset_printer =  new ResetPrinterCmd();
    std::string ss = reset_printer->serialize();

    TransportPedk *trans = TransportPedk::get_instance();
    LOG_I("MSG_END_RES ->");
    trans->send(ss);
}


void AppCmdPedkExec::app_get_session_id(const std::string& app_name)
{
    SessionGenerator& session_generator = SessionGenerator::get_instance();
    uint32_t session_id = session_generator.getNextSessionId();
    RetSessionCmd *ret_session_cmd =  new RetSessionCmd(session_id);
    std::string ss = ret_session_cmd->serialize();
    delete ret_session_cmd;
    TransportPedk *trans = TransportPedk::get_instance();
    LOG_I("MSG_APP_RETSESSION_DATA ->");
    trans->send_to_app(app_name, ss);
}
