#ifndef _HEARTBEAT_CMD_H_
#define _HEARTBEAT_CMD_H_

#include "cmd/cmds/app_cmds/app_cmds.h"
#include "cmd/exec/app_cmd_exec.h"

typedef struct AppHeartBeatCmdFormat {
    uint32_t type; // 指令类型
    std::string app_name;
    
    MSGPACK_DEFINE(type, app_name);
} AppHeartBeatCmdFormat;

class AppHeartBeatCmd: public AppCmd{
public:
    AppHeartBeatCmd(std::string app_name);
    AppHeartBeatCmd();
    ~AppHeartBeatCmd();
    uint32_t execute(AppCmdExec *app_cmd_exec) override;
    std::string serialize() override;
    uint32_t deserialize(std::string ss) override;
protected:
private:
    AppHeartBeatCmdFormat m_app_heartbeat_cmd;
};

#endif // _HEARTBEAT_CMD_H_