#ifndef RET_GET_SESSION_CMD_H
#define RET_GET_SESSION_CMD_H



#include "cmd/cmds/app_cmds/app_cmds.h"
#include "cmd/exec/app_cmd_exec.h"

typedef struct RetSessionidFormat {
    uint32_t type; // 指令类型
    uint32_t session_id;

    MSGPACK_DEFINE(type,session_id);
} RetSessionidFormat;

class RetSessionCmd : public AppCmd {
public:
    RetSessionCmd();
   // RetGetSessionCmd(RetGetSessionidFormat ret_get_session_cmd);
    RetSessionCmd(uint32_t session_id);
    ~RetSessionCmd();
    uint32_t execute(AppCmdExec *app_cmd_exec) override;
    std::string serialize() override;
    uint32_t deserialize(std::string ss) override;
    uint32_t get_session_id() const {return m_ret_session_cmd.session_id;}  
protected:
private:
    RetSessionidFormat m_ret_session_cmd;
};


#endif // RET_GET_SESSION_CMD_H
