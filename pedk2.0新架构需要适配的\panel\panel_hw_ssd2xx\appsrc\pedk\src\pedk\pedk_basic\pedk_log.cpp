#include <iostream>
#include <stdarg.h>
#include <fstream>
#include "basic/config.h"
#include "pedk/pedk_basic/pedk_log.h"
#include "basic/utils/file_utils.h"

#define PEDK_LOG PEDK_APP_WORKSPACE_PATH "pedk.log"          //pedk主进程log文件名及路径
#define PEDK_LOG_OLD PEDK_APP_WORKSPACE_PATH "pedk_old.log"  //pedk主进程备份log文件名及路径

/**
 * @brief 构造函数
 *       （1）LOG_TERMINAL模式：无需操作
 *       （2）LOG_FILE模式：需要删除pedk_old.log,以新建方式，打开pedk.log，并获取log文件描述符，为文件锁提供。
 *       （3）LOG_FILE_RELEASE模式：需要删除pedk_old.log,以新建方式，打开pedk.log，并初始化行号。
 * 
 * @param log_config 
 */
PedkLog::PedkLog(LogConfig log_config)
{
    m_log_config = log_config;
    if(m_log_config.m_log_type == LOG_FILE ){
        FileUtils::delete_file(PEDK_LOG_OLD);                    // 删除备份log（如果存在）
        m_pedk_log.log_ofstream.open(PEDK_LOG, std::ios::trunc); // 打开log文件
        m_pedk_log.log_fd = FileUtils::open_file(PEDK_LOG);      // 记住文件fd
    }else if(m_log_config.m_log_type == LOG_FILE_RELEASE){
        FileUtils::delete_file(PEDK_LOG_OLD);                    // 删除备份log（如果存在）
        m_pedk_log.log_ofstream.open(PEDK_LOG, std::ios::trunc); // 打开log文件
        m_pedk_log.line_num = 1;                                 // log初始化行号
    }
}

PedkLog::~PedkLog()
{
}

void PedkLog::log_out(LogLevel log_level, std::string tag, const char* format, ...)
{
    bool out_permit = false;
    
    //1.判断是否允许输出
    if(log_level >= m_log_config.m_log_level) {
        out_permit = true;
    }

    //2.当允许输出时，输出log
    if (out_permit == true) {
        //2.1 获取时间
        std::string time_str = get_time();

       //2.2 log格式转换，成字符串
	    va_list args;
        va_start(args, format);
        
        char buf[256];    
        std::string buffer;
        int len = vsnprintf(buf, sizeof(buf), format, args);

        if(0 <= len && len <  sizeof(buf)){
            //如果format长度没超buf，则直接使用
            std::string output(buf);
            va_end(args);
            buffer = output;
        }else{
            //如果format超长。则需要重新分配长度
            va_end(args);
            
            va_start(args, format);
            char *result = new char[len+1];
            len = vsnprintf(result, len+1, format, args);
            std::string output(result); 
            delete[] result;
            va_end(args);
            
            buffer = output;
        }

        //2.3 根据log模式不同，输出到不同地方
        switch (m_log_config.m_log_type) {
        case LOG_TERMINAL:
            std::cout << "[" << time_str << "][pedk][" << log_level_to_str(log_level) << "][" << tag << "]" << buffer << std::endl;
            break;
        case LOG_FILE:
            if (m_pedk_log.log_ofstream.is_open()) {
                FileUtils::lock_file(m_pedk_log.log_fd);//锁文件
                // 将log数据流导入log文件
                m_pedk_log.log_ofstream.seekp(0,std::ios::end);
                m_pedk_log.log_ofstream << "[" << time_str << "][pedk][" << log_level_to_str(log_level) << "][" << tag << "]" << buffer << std::endl;
                FileUtils::unlock_file(m_pedk_log.log_fd);//解文件
            }
            break;
        case LOG_FILE_RELEASE:
            if (m_pedk_log.log_ofstream.is_open()) {
                // 将log数据流导入log文件
                m_pedk_log.log_ofstream << "[" << time_str << "][pedk][" << log_level_to_str(log_level) << "][" << tag << "]" << buffer << std::endl;
                // 行号+1
                m_pedk_log.line_num++;

                check_line_and_rename(m_pedk_log, PEDK_LOG_OLD, PEDK_LOG);
            }
            break;
        }
    }
}


void PedkLog::log_out2(LogLevel log_level, std::string tag, const char* format, ...)
{
    //do nothing
}
 
 const char* logo_char = R"EOF(
 *****************************
 ____   ______ ____   __   __
 |  _ \ | ____||  _ \ | | / /
 | |_) /|  _|  | | | || |/ / 
 |  __/ | |___ | |_| || |\ \ 
 |_|    |_____||_ _ / |_| \_\
 ver:2.01.000
 )EOF";

void PedkLog::logo_out()
{
     switch (m_log_config.m_log_type) {
        case LOG_TERMINAL:
            std::cout << logo_char << std::endl;
            break;
        case LOG_FILE:
            if (m_pedk_log.log_ofstream.is_open()) {
                FileUtils::lock_file(m_pedk_log.log_fd);//锁文件
                // 将log数据流导入log文件
                m_pedk_log.log_ofstream << logo_char << std::endl;
                FileUtils::unlock_file(m_pedk_log.log_fd);//解文件
            }
            break;
        case LOG_FILE_RELEASE:
            if (m_pedk_log.log_ofstream.is_open()) {
                // 将log数据流导入log文件
                m_pedk_log.log_ofstream << logo_char << std::endl;
            }
            break;
        }
}