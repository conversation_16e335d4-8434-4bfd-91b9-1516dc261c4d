#ifndef _JSON_IO_H_
#define _JSON_IO_H_

#include <nlohmann/json.hpp>
#include <iostream>
#include "basic/config.h"
#include "basic/log/log.h"


/* 安装pedk的配置，从installer_config.json文件中获取 */
typedef struct InstallerConfig{
    std::string m_root_app;
    int64_t m_start_time;
    int64_t m_end_time;
}InstallerConfig;

/* 安装app的配置， */
typedef struct ProjectConfig{
    int64_t start_time;
    int64_t end_time;
    bool privileged_app;
    bool system_app;
    bool autoboot;
    bool icon_display;
    bool root_app;
}ProjectConfig;


/* JsonIO 类，使用nlohmann/json.hpp库帮助解析json格式的数据*/
class JsonIO {
public:
    static LogConfig  get_log_config(std::string log_config_file_path);
    static InstallerConfig  get_installer_config(std::string installer_config_file_path);   //加载installer_config.json配置文件全部信息
    static ProjectConfig get_project_config_json(std::string json_str);                     //加载projec.config.json配置文件全部信息
protected:
private:
    static enum LogType str_to_log_type(std::string log_type);
    static enum LogLevel str_to_log_level(std::string log_level);
};

using json = nlohmann::json;

#endif // _JSON_IO_H_