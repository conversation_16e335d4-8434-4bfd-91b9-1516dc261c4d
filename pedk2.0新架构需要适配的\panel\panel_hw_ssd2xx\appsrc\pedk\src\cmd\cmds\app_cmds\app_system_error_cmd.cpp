#include "app_system_error_cmd.h"

AppSystemErrorCmd::AppSystemErrorCmd()
{
    m_app_system_error_cmd.type = MSG_APP_SYS_ERR;
    m_app_system_error_cmd.error_type = "";
    m_app_system_error_cmd.error_info = "";
}

AppSystemErrorCmd::AppSystemErrorCmd(std::string error_type,std::string error_info)
{
    m_app_system_error_cmd.type = MSG_APP_SYS_ERR;
    m_app_system_error_cmd.error_type = error_type;
    m_app_system_error_cmd.error_info = error_info;
}

AppSystemErrorCmd::~AppSystemErrorCmd()
{
}

uint32_t AppSystemErrorCmd::execute(AppCmdExec *app_cmd_exec)
{
    app_cmd_exec->app_error_info(m_app_system_error_cmd.error_type,m_app_system_error_cmd.error_info);

    return 0;
}

std::string AppSystemErrorCmd::serialize()
{
    std::stringstream ss;
    msgpack::pack(ss, m_app_system_error_cmd);
    return ss.str();
}

uint32_t AppSystemErrorCmd::deserialize(std::string ss)
{
    //解码
    msgpack::object_handle oh = msgpack::unpack(ss.c_str(),ss.size());
    msgpack::object obj = oh.get();

    //将解码数据放到内部结构体中
    obj.convert(m_app_system_error_cmd);

    LOG_I("Data deserialize");

    return 0;
}