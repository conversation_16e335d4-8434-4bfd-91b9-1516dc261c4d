#include "ret_session_cmd.h"


RetSessionCmd::RetSessionCmd()
{
     
}
RetSessionCmd::~RetSessionCmd()
{
}

RetSessionCmd::RetSessionCmd(uint32_t session_id)
{
    m_ret_session_cmd.type = MSG_RET_SESSION_ID;
    m_ret_session_cmd.session_id = session_id;
}

uint32_t RetSessionCmd::execute(AppCmdExec *app_cmd_exec)
{
    std::cout<<"RetGetSessionCmd execute"<<std::endl;
    return 0;
}

std::string RetSessionCmd::serialize()
{
    std::stringstream ss;
    msgpack::pack(ss, m_ret_session_cmd);

    return ss.str();
}

uint32_t RetSessionCmd::deserialize(std::string ss)
{
    // 解码
    msgpack::object_handle oh = msgpack::unpack(ss.c_str(), ss.size());
    msgpack::object obj = oh.get();

    // 将解码数据放到内部结构体中
    obj.convert(m_ret_session_cmd);

    return 0;
}
