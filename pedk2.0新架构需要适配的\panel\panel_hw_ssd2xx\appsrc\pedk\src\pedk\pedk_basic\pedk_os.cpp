#include "pedk_os.h"
#include <unistd.h>
#include <sys/wait.h>
#include <dirent.h>
#include <csignal>
#include "pedk/pedk_manager/app.h"
#include "pedk/pedk_manager/app_manager.h"
#include <iostream>
#include <map>
#include <fstream>
#include <string>
#include <sys/stat.h>
#include <fcntl.h>

/**
 * @brief 捕捉ctrl+c信号，当pedk进程被ctrl+c关闭前，把全部子进程杀死。
 * 
 * @param signal 
 */
void signal_handler(int signal)
{
    AppManager *app_manager = AppManager::get_instance();
    std::map<std::string, App> apps = app_manager->get_apps();
    std::map<std::string, App>::iterator iter;
    for(iter = apps.begin(); iter != apps.end(); ++iter){
        App app = iter->second;
        app.kill_self();
    }

    exit(signal);
}

void PedkOS::init_signal_handler()
{
    signal(SIGINT, signal_handler);
}

bool create_directory(const std::string &target_dir)
{
    int status = mkdir(target_dir.c_str(), 0777);
    return (status == 0 || errno == EEXIST);
}

void PedkOS::cp_config(const std::string &target_dir, const std::string &target_config, const std::string &src_config)
{
    if(!create_directory(target_dir)){
        std::cout << "Dir not exist:"<< target_dir <<std::endl;
    }

    std::ifstream source_file(src_config);
    std::ofstream target_file(target_config);

    if(!source_file.is_open() || !target_file.is_open()){
        std::cout << "Open config file fail!" <<std::endl;
        std::cout << src_config <<std::endl;
        std::cout << target_config <<std::endl;
        return;
    }

    target_file << source_file.rdbuf();
    ::sync();
}
