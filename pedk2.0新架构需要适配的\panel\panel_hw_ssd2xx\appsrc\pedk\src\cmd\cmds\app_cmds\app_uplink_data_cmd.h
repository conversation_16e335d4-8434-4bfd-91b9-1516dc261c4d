#ifndef _APP_UPLINK_DATA_CMD_H_
#define _APP_UPLINK_DATA_CMD_H_

#include "cmd/cmds/app_cmds/app_cmds.h"
#include "cmd/exec/app_cmd_exec.h"

typedef struct AppUplinkDataCmdFormat {
    uint32_t type;        // 指令类型
    std::string target;
    std::string msg;      // 消息

    MSGPACK_DEFINE(type,target, msg);
} AppUplinkDataCmdFormat;

class AppUplinkDataCmd: public AppCmd{
public:
    AppUplinkDataCmd();
    AppUplinkDataCmd(std::string target, std::string msg);
    ~AppUplinkDataCmd();
    uint32_t execute(AppCmdExec *app_cmd_exec) override;
    std::string serialize() override;
    uint32_t deserialize(std::string ss) override;
protected:
private:
    AppUplinkDataCmdFormat m_app_uplink_data_cmd;
};


#endif // _APP_UPLINK_DATA_CMD_H_
