#ifndef _OS_H_
#define _OS_H_

#include <iostream>
#include <vector>
#include "os_information.h"
//#include <filesystem>

//与操作系统有关的操作，封装到OS工具类中
class OS{
public:
    static bool start_app(std::string app_name, OsInformation &os_info);
    static time_t get_systime();
    static std::string time_to_string(time_t *tm);
    static bool wait_app_exit(OsInformation os_info);
    static void kill_app(OsInformation os_info);
    static std::vector<std::string> get_directories(std::string folderPath);
    static std::vector<std::string> get_data_file(std::string folderPath);
    static void copy_app(const std::string &destDir, const std::string &sourceDir);
    static void pedk_sleep(unsigned int sleep_second);
    static void app_exit();
    static void check_data_dir_and_make(const std::string &app_path);
    static int file_open(std::string file_path);
    static int file_lock(int fd);
    static int file_unlock(int fd);
    static uint32_t count_app_dir (const std::string& folderPath);
private:
   // static void copy_folder(const std::filesystem::path& source, const std::filesystem::path& destination);
};

#endif // _OS_H_