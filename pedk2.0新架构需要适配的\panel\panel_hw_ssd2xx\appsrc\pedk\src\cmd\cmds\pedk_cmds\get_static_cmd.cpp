#include "get_static_cmd.h"

GetStaticCmd::GetStaticCmd()
{
    m_get_static_cmd.type = MSG_GET_STATIC_PROPERTY;
    m_get_static_cmd.app_name = "";
}

GetStaticCmd::GetStaticCmd(std::string app_name)
{
    m_get_static_cmd.type = MSG_GET_STATIC_PROPERTY;
    m_get_static_cmd.app_name = app_name;
}

GetStaticCmd::~GetStaticCmd()
{
}

uint32_t GetStaticCmd::execute(PedkCmdExec *pedk_cmd_exec)
{
    pedk_cmd_exec->get_static_property(m_get_static_cmd.app_name);
   
    return 0;
}

std::string GetStaticCmd::serialize()
{
    std::stringstream ss;
    msgpack::pack(ss, m_get_static_cmd);

    return ss.str();
}

uint32_t GetStaticCmd::deserialize(std::string ss)
{
    // 解码
    msgpack::object_handle oh = msgpack::unpack(ss.c_str(), ss.size());
    msgpack::object obj = oh.get();

    // 将解码数据放到内部结构体中
    obj.convert(m_get_static_cmd);

    return 0;
}