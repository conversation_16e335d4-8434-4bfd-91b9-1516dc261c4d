#include "config.h"
#include "basic/json_io/json_io.h"
#include "basic/log/log.h"

Config *Config::m_instance = nullptr;

Config::Config()
{
}

Config::~Config()
{
}

Config *Config::get_instance()
{
    if (m_instance == nullptr) {
        m_instance = new Config();
    }

    return m_instance;
}


void Config::set_app_name(const char *app_name)
{
    m_app_name = app_name;
}

std::string Config::get_app_name()
{
    return m_app_name;
}
