//此配置文件，根据机种不同会不同
//路径
#define PEDK_EXECUTABLE_PATH        "/customer/pedk/"          // PEDK执行路径，只读
#define PEDK_WORKSPACE_PATH         "/customer/pedk/"          // PEDK工作空间路径，只读
#define PEDK_SYS_APP_WORKSPACE_PATH "/customer/pedk/sys_apps/" // PEDK系统APP存放路径，只读
#define PEDK_DEPEND_JS_PATH         "/customer/pedk/depend_js/"// PEDK依赖脚本存放路径，只读
#define PEDK_APP_WORKSPACE_PATH     "/pedk/"                   // PEDK普通APP存放路径，读写

//映射路径
#define RESOURCES_PATH  "/pedk/"
#define STORAGE_PATH    "/pedk/"
#define TEMP_PATH       "/tmp/pedk/"
#define DC_STORAGE_PATH "/pedk_apps/"
#define DC_TEMP_PATH    "/tmp/pedk/"
#define CACHE_PATH      "/data/cache/"
#define SHARE_PATH      "/media/usbms/"

// 文件
#define PEDK_LOG_CONFIG_PATH PEDK_APP_WORKSPACE_PATH "config.json"     					// pedk进程/pedk_app进程的配置文件
#define PEDK_INSTALLER_CONFIG_PATH PEDK_APP_WORKSPACE_PATH "installer_config.json"      // pedk进程的安装配置文件
#define PEDK_LOG_STANDBY_CONFIG_PATH PEDK_WORKSPACE_PATH "config.json"     				// pedk进程/pedk_app进程的配置文件
#define PEDK_INSTALLER_STANDBY_CONFIG_PATH PEDK_WORKSPACE_PATH "installer_config.json"  // pedk进程和pedk_app进程的log配置文件备份
#define PEDK_APP_EXEC_FILE PEDK_EXECUTABLE_PATH "pedk_app.elf"                          // pedk应用的进程名
#define PEDK_APP_CONFIG_FILENAME "/project.config.json"            						// pedk应用的配置文件
#define PEDK_APP_STATIC_PROPERTY_FILENAME "/app.json"              						// pedk应用的静态属性文件
#define PEDK_APP_ICON "/resources/media/icon/icon.png"                   				// pedk应用的图标文件
#define PEDK_APP_ICON2 "/resources/media/icon/icon.bmp"                  				// pedk应用的图标文件
#define PEDK_APP_ICON3 "/resources/media/icon/icon.jpeg"                 				// pedk应用的图标文件

// 数字定义
#define END_WAIT_TIME 10 // 退出app时，最大等待时间s

// zmq port口设定
#define TRANS_ROUTER "ipc:///tmp/ipc_rd"
#define TRANS_DEALER "ipc:///tmp/ipc_rd"
#define TRANS_PUB "ipc:///tmp/ipc_ps"
#define TRANS_SUB "ipc:///tmp/ipc_ps"

// 客户端id
//#define CLIENT_NAME "pantum_ui"
#define CLIENT_NAME "panel_app"
