#ifndef _UV_MANAGER_H_
#define _UV_MANAGER_H_
#include <uv.h>
#include "transport/transport.h"

#define HEARTBEAT_TIME 15000

/* libuv管理类 */
/* 与libuv相关的操作封装在此类中 */
class uvManager {
public:
    uvManager(Transport *trans);
    ~uvManager();
    virtual void run() = 0;

    void stop();
    bool is_stop();

protected:
    uv_loop_t* m_uv_loop; // 循环句柄
    uv_poll_t m_uv_poll; // poll句柄
    
    bool m_stop_flag;    //停止标志
    Transport *m_trans;  //传输层句柄
    uv_timer_t m_heart_beat;   //心跳句柄
private:
};

#endif // _UV_MANAGER_H_