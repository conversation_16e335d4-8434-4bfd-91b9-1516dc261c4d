#ifndef _ERROR_INFO_CMD_H_
#define _ERROR_INFO_CMD_H_

#include "cmd/cmds/pedk_cmds/pedk_cmds.h"
#include "cmd/exec/pedk_cmd_exec.h"

typedef struct ErrorInfoCmdFormat {
    uint32_t type;          // 指令类型
    std::string error_type; // 预留
    std::string error_info; // 错误信息
    MSGPACK_DEFINE(type, error_type, error_info);
} ErrorInfoCmdFormat;

class ErrorInfoCmd: public PedkCmd{
public:
    ErrorInfoCmd();
    ErrorInfoCmd(const std::string& err_type, const std::string& err_info);
    ~ErrorInfoCmd();
    uint32_t execute(PedkCmdExec *pedk_cmd_exec) override;
    std::string serialize() override;
    uint32_t deserialize(std::string ss) override;
protected:
private:
    ErrorInfoCmdFormat m_error_info_cmd;
};

#endif // _ERROR_INFO_CMD_H_