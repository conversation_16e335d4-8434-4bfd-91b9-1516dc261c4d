
//#include <memory.h>
#include <uv.h>
#include <zmq.h>
#include <msgpack.hpp>
#include <iostream>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <cstdlib>

#include "basic/config.h"
#include "basic/log/log.h"
#include "basic/log/log_manager.h"
#include "basic/json_io/json_io.h"
#include "cmd/context/cmd_context.h"
#include "transport/transport.h"
#include "manager/uv_manager.h"
#include "pedk/pedk_manager/app_manager.h"
#include "pedk/pedk_basic/pedk_log.h"
#include "pedk/pedk_cmd/pedk_cmd_context.h"
#include "pedk/pedk_transport/transport_pedk.h"
#include "pedk/pedk_manager/uv_manager_pedk.h"
#include "pedk/pedk_basic/pedk_json_io.h"
#include "pedk/pedk_basic/pedk_os.h"
#include "basic/utils/file_utils.h"

/**
 * @brief PEDK APP初始化
 *
 * @param argc
 * @param argv
 */
static void pedk_app_init()
{
    // 1.获取pedkapp程序的配置信息
    Config* config = Config::get_instance();
    config->set_app_name("pedk");

    // 2.在PEDK_WORKSPACE_PATH路径下会存放一份只读的备用配置文件。
    //当PEDK_APP_WORKSPACE_PATH路径下的可读写配置文件不存在时，先拷贝一份备用文件，然后再使用
    if(false == FileUtils::file_exist(PEDK_LOG_CONFIG_PATH)){
        PedkOS::cp_config(PEDK_APP_WORKSPACE_PATH, PEDK_LOG_CONFIG_PATH, PEDK_LOG_STANDBY_CONFIG_PATH);
    }
    if(false == FileUtils::file_exist(PEDK_INSTALLER_CONFIG_PATH)){
        PedkOS::cp_config(PEDK_APP_WORKSPACE_PATH, PEDK_INSTALLER_CONFIG_PATH, PEDK_INSTALLER_STANDBY_CONFIG_PATH);
    }

    // 3.启动pedk log，将pedk log对象放到LogManager中，提供log使用。
    PedkLog *pedk_log = new PedkLog(JsonIO::get_log_config(PEDK_LOG_CONFIG_PATH));
    LogManager::get_instance()->set_log(pedk_log);

    // 4.PEDK主进程分配pedk cmd上下文
    PedkCmdContext *pedk_cmd_context = PedkCmdContext::get_instance();

    PedkOS::init_signal_handler();
}

int main(int argc, char** argv)
{
    // 1.通用初始化
    pedk_app_init();
    LogManager::get_instance()->get_log()->logo_out();

    // 2.启动传输功能
    Transport *trans = (Transport*)TransportPedk::get_instance();
    if (nullptr == trans) {
        //启动失败
        LOG_F("Transport start Fail");
        return -1;
    }

    AppManager* app_manager = AppManager::get_instance(); 
    // 3.检查系统APP，如果丢失则将系统APP拷贝到APP启动路径
    app_manager->check_sys_app();

    // 4.启动APP心跳检查
    uvManagerPedk *uv_manager = new uvManagerPedk(trans);
    uv_manager->start_heartbeat_check();

    // 5.启动uv_loop()
    while(1){
        uv_manager->run();
    }
    return 0;
}

/***
 *      ┌─┐       ┌─┐ + +
 *   ┌──┘ ┴───────┘ ┴──┐++
 *   │                 │
 *   │       ───       │++ + + +
 *   ███████───███████ │+
 *   │                 │+
 *   │       ─┴─       │
 *   │                 │
 *   └───┐         ┌───┘
 *       │         │
 *       │         │   + +
 *       │         │
 *       │         └──────────────┐
 *       │                        │
 *       │                        ├─┐
 *       │                        ┌─┘
 *       │                        │
 *       └─┐  ┐  ┌───────┬──┐  ┌──┘  + + + +
 *         │ ─┤ ─┤       │ ─┤ ─┤
 *         └──┴──┘       └──┴──┘  + + + +
 *                神兽保佑
 *               代码无BUG!
 */