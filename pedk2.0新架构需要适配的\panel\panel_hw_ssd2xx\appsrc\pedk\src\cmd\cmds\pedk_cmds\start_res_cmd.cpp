#include "start_res_cmd.h"

StartResCmd::StartResCmd()
{
    m_start_res_cmd.type = MSG_START_RES;
    m_start_res_cmd.app_name = "";
    m_start_res_cmd.result = false;
}

StartResCmd::StartResCmd(std::string app_name, bool res)
{
    m_start_res_cmd.type = MSG_START_RES;
    m_start_res_cmd.app_name = app_name;
    m_start_res_cmd.result = res;
}

StartResCmd::~StartResCmd()
{
}

uint32_t StartResCmd::execute(PedkCmdExec *pedk_cmd_exec)
{
    pedk_cmd_exec->start_res(m_start_res_cmd.app_name, m_start_res_cmd.result);

    return 0;
}

std::string StartResCmd::serialize()
{
    std::stringstream ss;
    msgpack::pack(ss, m_start_res_cmd);
    return ss.str();
}

uint32_t StartResCmd::deserialize(std::string ss)
{
    //解码
    msgpack::object_handle oh = msgpack::unpack(ss.c_str(),ss.size());
    msgpack::object obj = oh.get();

    //将解码数据放到内部结构体中
    obj.convert(m_start_res_cmd);

    return 0;
}