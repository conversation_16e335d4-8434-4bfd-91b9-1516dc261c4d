#ifndef _APP_FRONT_CMD_H_
#define _APP_FRONT_CMD_H_

#include "cmd/cmds/app_cmds/app_cmds.h"
#include "cmd/exec/app_cmd_exec.h"

typedef struct AppFrontCmdFormat {
    uint32_t type; // 指令类型

    MSGPACK_DEFINE(type);
} AppFrontCmdFormat;

class AppFrontCmd: public AppCmd{
public:
    AppFrontCmd();
    ~AppFrontCmd();
    uint32_t execute(AppCmdExec *app_cmd_exec) override;
    std::string serialize() override;
    uint32_t deserialize(std::string ss) override;
protected:
private:
    AppFrontCmdFormat m_app_front_cmd;
};

#endif // _APP_FRONT_CMD_H_