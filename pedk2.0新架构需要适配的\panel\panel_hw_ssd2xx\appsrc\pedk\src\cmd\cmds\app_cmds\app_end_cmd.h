#ifndef _APP_END_CMD_H_
#define _APP_END_CMD_H_

#include "cmd/cmds/app_cmds/app_cmds.h"
#include "cmd/exec/app_cmd_exec.h"

typedef struct AppEndCmdFormat {
    uint32_t type; // 指令类型

    MSGPACK_DEFINE(type);
} AppEndCmdFormat;

class AppEndCmd: public AppCmd{
public:
    AppEndCmd();
    ~AppEndCmd();
    uint32_t execute(AppCmdExec *app_cmd_exec) override;
    std::string serialize() override;
    uint32_t deserialize(std::string ss) override;
protected:
private:
    AppEndCmdFormat m_app_end_cmd;
};

#endif // _APP_END_CMD_H_