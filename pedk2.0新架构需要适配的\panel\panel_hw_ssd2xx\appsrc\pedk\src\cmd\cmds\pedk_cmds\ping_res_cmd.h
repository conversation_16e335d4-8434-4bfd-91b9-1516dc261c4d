#ifndef _PING_RES_CMD_H_
#define _PING_RES_CMD_H_

#include "cmd/cmds/pedk_cmds/pedk_cmds.h"
#include "cmd/exec/pedk_cmd_exec.h"

typedef struct PingResCmdFormat {
    uint32_t type; // 指令类型
    bool certificate_result;//证书验证结果

    MSGPACK_DEFINE(type, certificate_result);
} PingResCmdFormat;

class PingResCmd : public PedkCmd{
public:
    PingResCmd();
    PingResCmd(bool certificate_result);
    ~PingResCmd();
    uint32_t execute(PedkCmdExec *pedk_cmd_exec) override;
    std::string serialize() override;
    uint32_t deserialize(std::string ss) override;

protected:
private:
    
    PingResCmdFormat m_ping_res_cmd;
};

#endif // _PING_RES_CMD_H_