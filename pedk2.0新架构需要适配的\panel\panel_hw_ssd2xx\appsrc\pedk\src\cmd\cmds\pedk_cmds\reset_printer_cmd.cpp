#include "cmd/cmds/pedk_cmds/reset_printer_cmd.h"

ResetPrinterCmd::ResetPrinterCmd()
{
    m_reset_printer_cmd.type = MSG_RESET_PRINTER;
}
    
ResetPrinterCmd::~ResetPrinterCmd()
{
}

uint32_t ResetPrinterCmd::execute(PedkCmdExec *pedk_cmd_exec)
{
    pedk_cmd_exec->reset_printer();
    return 0;
}

std::string ResetPrinterCmd::serialize()
{
    std::stringstream ss;
    msgpack::pack(ss, m_reset_printer_cmd);

    return ss.str();
}

uint32_t ResetPrinterCmd::deserialize(std::string ss)
{
    //解码
    msgpack::object_handle oh = msgpack::unpack(ss.c_str(),ss.size());
    msgpack::object obj = oh.get();

    //将解码数据放到内部结构体中
    obj.convert(m_reset_printer_cmd);

    return 0;
}