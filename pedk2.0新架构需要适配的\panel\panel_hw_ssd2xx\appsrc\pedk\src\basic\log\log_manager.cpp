#include "basic/log/log_manager.h"

LogManager* LogManager::m_instance = nullptr;

LogManager::LogManager()
{
    m_log = nullptr;
}
    
LogManager::~LogManager()
{
    if(m_log != nullptr){
       delete m_log;
    }

    if(m_instance != nullptr){
        delete m_instance;
    }
}

LogManager* LogManager::get_instance()
{
    if (m_instance == nullptr) {
        m_instance = new LogManager();
    }

    return m_instance;
}

Log* LogManager:: get_log()
{
    return m_log;
}


void LogManager:: set_log(Log* log)
{
    m_log = log;
}

