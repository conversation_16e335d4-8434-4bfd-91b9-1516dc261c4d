#ifndef _APP_RESET_CMD_H_
#define _APP_RESET_CMD_H_

#include "cmd/cmds/app_cmds/app_cmds.h"
#include "cmd/exec/app_cmd_exec.h"

typedef struct AppResetCmdFormat {
    uint32_t type; // 指令类型
    std::string app_name;

    MSGPACK_DEFINE(type, app_name);
} AppResetCmdFormat;

class AppResetCmd: public AppCmd{
public:
    AppResetCmd(std::string app_name);
    AppResetCmd();
    ~AppResetCmd();
    uint32_t execute(AppCmdExec *app_cmd_exec) override;
    std::string serialize() override;
    uint32_t deserialize(std::string ss) override;
protected:
private:
    AppResetCmdFormat m_app_exit_cmd;
};

#endif // _APP_RESET_CMD_H_
