#include "error_info_cmd.h"

ErrorInfoCmd::ErrorInfoCmd(const std::string& error_type, const std::string& error_info)
{
    m_error_info_cmd.error_type = error_type;
    m_error_info_cmd.error_info = error_info;
    m_error_info_cmd.type = MSG_ERROR_INFO;
    LOG_D("ErrorInfoCmd by format");
}

ErrorInfoCmd::ErrorInfoCmd()
{
    m_error_info_cmd.type = MSG_ERROR_INFO;
}

ErrorInfoCmd::~ErrorInfoCmd()
{
}

uint32_t ErrorInfoCmd::execute(PedkCmdExec *pedk_cmd_exec)
{
    std::cout<<"ErrorInfoCmd execute"<<std::endl;
    pedk_cmd_exec->error_info(m_error_info_cmd.error_type, m_error_info_cmd.error_info);

    return 0;
}

std::string ErrorInfoCmd::serialize()
{
    std::stringstream ss;
    msgpack::pack(ss, m_error_info_cmd);

    return ss.str();
}

uint32_t ErrorInfoCmd::deserialize(std::string ss)
{
    // 解码
    msgpack::object_handle oh = msgpack::unpack(ss.c_str(), ss.size());
    msgpack::object obj = oh.get();

    // 将解码数据放到内部结构体中
    obj.convert(m_error_info_cmd);

    std::cout << m_error_info_cmd.error_type << std::endl;
    std::cout << m_error_info_cmd.error_info << std::endl;
    return 0;
}