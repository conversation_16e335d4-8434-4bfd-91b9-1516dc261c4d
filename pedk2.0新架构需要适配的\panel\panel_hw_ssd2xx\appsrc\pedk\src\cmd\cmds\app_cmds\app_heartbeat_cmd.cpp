#include "app_heartbeat_cmd.h"

AppHeartBeatCmd::AppHeartBeatCmd(std::string app_name)
{
    m_app_heartbeat_cmd.type = MSG_APP_HEART_BEAT;
    m_app_heartbeat_cmd.app_name = app_name;
}

AppHeartBeatCmd::AppHeartBeatCmd()
{
    m_app_heartbeat_cmd.type = MSG_APP_HEART_BEAT;
    m_app_heartbeat_cmd.app_name = "";
}

AppHeartBeatCmd::~AppHeartBeatCmd()
{
}

uint32_t AppHeartBeatCmd::execute(AppCmdExec *app_cmd_exec)
{
    app_cmd_exec->app_heart_beat(m_app_heartbeat_cmd.app_name);
   
    return 0;
}

std::string AppHeartBeatCmd::serialize()
{
    std::stringstream ss;
    msgpack::pack(ss, m_app_heartbeat_cmd);
    return ss.str();
}

uint32_t AppHeartBeatCmd::deserialize(std::string ss)
{
    //解码
    msgpack::object_handle oh = msgpack::unpack(ss.c_str(),ss.size());
    msgpack::object obj = oh.get();

    //将解码数据放到内部结构体中
    obj.convert(m_app_heartbeat_cmd);

    return 0;
}