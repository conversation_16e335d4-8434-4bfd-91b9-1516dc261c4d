#include "basic/json_io/json_io.h"
#include <fstream>

/**
 * @brief 从给定的文件路径中，获取log配置。log配置文件以json格式存储。
 *         log配置参考LogConfig结构。
 * @param log_config_file_path 
 * @return LogConfig 
 */
LogConfig JsonIO:: get_log_config(std::string log_config_file_path)
{
    std::cout<<"parsin config"<<std::endl;
    LogConfig log_config;
   
    //设置log_config的默认值。
    log_config.m_log_type = LOG_TERMINAL;
    log_config.m_log_level = PEDK_LOG_LEVEL_DEBUG;
    log_config.m_log_file_max_line = 5000;

    //尝试打开log配置文件
    std::ifstream log_config_file;
    log_config_file.open(log_config_file_path);
    if(log_config_file.is_open()){
        //如果能够打开log配置文件，则进行解析，更新配置信息。
        try {
            json json_data;
            json_data = json::parse(log_config_file);

            if (json_data["LogType"].is_string()) {
                log_config.m_log_type = str_to_log_type(json_data["LogType"].get<std::string>());

            }

            if (json_data["LogLevel"].is_string()) {
                log_config.m_log_level = str_to_log_level(json_data["LogLevel"].get<std::string>());
            } 

            if (json_data["LogFileMaxLine"].is_number()) {
                log_config.m_log_file_max_line = json_data["LogFileMaxLine"].get<uint32_t>();
            } 
        } catch (json::exception& e) {
            std::cerr << "JSON error:" << e.what() << std::endl;
        }
    }

    return log_config;
}

/**
 * @brief 将log类型：字符串类型转换成枚举类型
 * 
 * @param log_type 
 * @return enum LogType 
 */
enum LogType JsonIO::str_to_log_type(std::string log_type)
{
    enum LogType ret = LOG_TERMINAL;

    if (log_type == "LOG_TERMINAL") {
        std::cout<<"LOG_TERMINAL"<<std::endl;
        ret = LOG_TERMINAL;
    } else if (log_type == "LOG_FILE") {
        std::cout<<"LOG_FILE"<<std::endl;
        ret = LOG_FILE;
    } else if (log_type == "LOG_FILE_RELEASE") {
        std::cout<<"LOG_FILE_RELEASE"<<std::endl;
        ret = LOG_FILE_RELEASE;
    } 
    
    return ret;
}

/**
 * @brief 将log级别：字符串类型转化成枚举类型
 * 
 * @param log_level 
 * @return enum LogLevel 
 */
enum LogLevel JsonIO:: str_to_log_level(std::string log_level)
{
    enum LogLevel ret = PEDK_LOG_LEVEL_DEBUG;
    
    if("LOG_LEVEL_DEBUG" == log_level) {
        ret = PEDK_LOG_LEVEL_DEBUG;
    }else if("LOG_LEVEL_INFO" == log_level){
        ret = PEDK_LOG_LEVEL_INFO;
    }else if("LOG_LEVEL_WARNING" == log_level){
        ret = PEDK_LOG_LEVEL_WARNING;
    }else if("LOG_LEVEL_ERROR" == log_level){
        ret = PEDK_LOG_LEVEL_ERROR;
    }else if("LOG_LEVEL_FATAL" == log_level){
        ret = PEDK_LOG_LEVEL_FATAL;
    }

    return ret;
}


/**
 * @brief 加载installer_config.json配置文件全部信息
 * 
 * @param installer_config_file_path 
 * @return InstallerConfig 
 */
InstallerConfig JsonIO::get_installer_config(std::string installer_config_file_path)
{
    InstallerConfig installer_config;
   
    //设置log_config的默认值。
    installer_config.m_root_app = "";
    installer_config.m_start_time = 0;
    installer_config.m_end_time = 0x7FFFFFFFFFFFFFFF;;

    //尝试打开log配置文件
    std::ifstream installer_config_file;
    installer_config_file.open(installer_config_file_path);
    if(installer_config_file.is_open()){
        //如果能够打开log配置文件，则进行解析，更新配置信息。
        try {
            json json_data;
            json_data = json::parse(installer_config_file);

            if (json_data["root_app"].is_string()) {
                installer_config.m_root_app = json_data["root_app"].get<std::string>();
            } 

            if (json_data["start_time"].is_number()) {
                installer_config.m_start_time = json_data["start_time"].get<int64_t>();
            } 

            if (json_data["end_time"].is_number()) {
                installer_config.m_end_time = json_data["end_time"].get<int64_t>();
            } 
        } catch (json::exception& e) {
            std::cerr << "JSON error:" << e.what() << std::endl;
        }
    }

    return installer_config;
}

/**
 * @brief 加载projec.config.json配置文件全部信息
 * 
 * @param json_str app中project.config.json文件中的内容
 * @return true 显示icon
 * @return false 不显示icon
 */
ProjectConfig JsonIO::get_project_config_json(std::string json_str)
{
    ProjectConfig ret;

    try {
        json json_data;
        json_data = json::parse(json_str);

        if (json_data["start_time"].is_number()) {
            ret.start_time = json_data["start_time"].get<int64_t>();
        } else {
            ret.start_time = 0;
        }

        if (json_data["valid_time"].is_number()) {
            ret.end_time = json_data["valid_time"].get<int64_t>();
        } else {
            ret.end_time = 0;
        }

        if (json_data["privileged_app"].is_boolean()) {
            ret.privileged_app = json_data["privileged_app"].get<bool>();
        } else {
            ret.privileged_app = false;
        }

        if (json_data["system_app"].is_boolean()) {
            ret.system_app = json_data["system_app"].get<bool>();
        } else {
            ret.system_app = false;
        }

        if (json_data["autoboot"].is_boolean()) {
            ret.autoboot = json_data["autoboot"].get<bool>();
        } else {
            ret.autoboot = false;
        }

        if (json_data["icon_display"].is_boolean()) {
            ret.icon_display = json_data["icon_display"].get<bool>();
        } else {
            ret.icon_display = false;
        }

        if (json_data["root_app"].is_boolean()) {
            ret.root_app = json_data["root_app"].get<bool>();
        } else {
            ret.root_app = false;
        }
    } catch (json::exception& e) {
        LOG_E("JSON error: %s",e.what());
    }

    return ret;
}
