#ifndef _RESET_PRINTER_CMD_H_
#define _RESET_PRINTER_CMD_H_

#include "cmd/cmds/pedk_cmds/pedk_cmds.h"
#include "cmd/exec/pedk_cmd_exec.h"

typedef struct ResetPrinterCmdFormat {
    uint32_t type; // 指令类型

    MSGPACK_DEFINE(type);
} ResetPrinterCmdFormat;

class ResetPrinterCmd : public PedkCmd{
public:
    ResetPrinterCmd();
    ~ResetPrinterCmd();
    uint32_t execute(PedkCmdExec *pedk_cmd_exec) override;
    std::string serialize() override;
    uint32_t deserialize(std::string ss) override;

protected:
private:
    
    ResetPrinterCmdFormat m_reset_printer_cmd;
};

#endif // _RESET_PRINTER_CMD_H_
