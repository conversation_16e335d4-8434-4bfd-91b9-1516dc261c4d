#ifndef _APP_MANAGER_H_
#define _APP_MANAGER_H_

#include "app.h"
#include <map>
#include "basic/json_io/json_io.h"
#define ABNORMAL_TIME 30

#include <vector>


class AppManager {
public:
    ~AppManager();
    static AppManager* get_instance();
    void add_app(App app);
    bool find_app(std::string app_name,App *app);
    bool delete_app(std::string app_name);
    std::map<std::string, App>& get_apps();

    void auto_start_app();
    void check_pedk();

    std::string check_hearbeat();
    void update_heartbeat(std::string app_name);
    void check_sys_app();
    void send_back_msg(std::string app_name);
    void send_front_msg(std::string app_name);
    
    InstallerConfig m_installer_config;
    bool m_pedk_enable;

protected:
private:
    AppManager();
    static AppManager* m_instance;
    std::map<std::string, App> m_apps;
    void auto_start_app_by_path(std::string path);
};

#endif // _APP_MANAGER_H_
