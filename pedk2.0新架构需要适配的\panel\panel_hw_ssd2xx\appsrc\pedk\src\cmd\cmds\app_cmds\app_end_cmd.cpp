#include "app_end_cmd.h"

AppEndCmd::AppEndCmd()
{
    m_app_end_cmd.type = MSG_APP_END;
}


AppEndCmd::~AppEndCmd()
{
}

uint32_t AppEndCmd::execute(AppCmdExec *app_cmd_exec)
{
    //执行一些退出流程，应该是调on.end函数
    app_cmd_exec->app_end();
    //Todo:
   /* uvManager* uv_manager = uvManagerFactor::get_uv_manager_instance();
    uv_manager->stop();
*/

    return 0;
}

std::string AppEndCmd::serialize()
{
    std::stringstream ss;
    msgpack::pack(ss, m_app_end_cmd);
    return ss.str();
}

uint32_t AppEndCmd::deserialize(std::string ss)
{
    //解码
    msgpack::object_handle oh = msgpack::unpack(ss.c_str(),ss.size());
    msgpack::object obj = oh.get();

    //将解码数据放到内部结构体中
    obj.convert(m_app_end_cmd);

    return 0;
}