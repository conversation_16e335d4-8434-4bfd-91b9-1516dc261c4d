#include "file_utils.h"
#include "basic/config.h"
#include <fstream>
#include <string>
#include <cstdio>

/**
 * @brief 读取文件全部内容，以string类型保存
 * 
 * @param file_name 
 * @return std::string 
 */
std::string FileUtils::load_file(std::string file_name)
{
    std::ifstream file;
    file.open(file_name);
    if(!file.is_open()){
        LOG_E("File [%s] open fail!",file_name.c_str());
        return "";
    }

    std::string content;
    std::string line;
    while(std::getline(file,line)){
        content += line+"\n";
    }

    file.close();

    return content;
}

/**
 * @brief 读取二进制文件全部内容，以vector<char>类型保存
 * 
 * @param file_name 
 * @return std::string 
 */
bool FileUtils::load_binary(std::string file_name, std::vector<char> &out_binary)
{
    std::ifstream file;

    file.open(file_name, std::ios::ate);
     if(!file.is_open()){
        LOG_E("File [%s] open fail!",file_name.c_str());
        return false;
    }

    //获取文件大小
    std::streampos size = file.tellg();
    file.seekg(0, std::ios::beg);
    
    std::vector<char> buffer(size);
    file.read(buffer.data(), size);
    out_binary = buffer;

    if(!file){
        LOG_E("Read binary [%s] error",file_name.c_str());
    }
    file.close();

    return true;
}

/**
 * @brief 存储string类型到文件中
 * 
 * @param file_stream 
 * @return true 
 * @return false 
 */
bool FileUtils::store_file(std::string file_name, std::string file_stream)
{
    std::ofstream file;
    file.open(file_name);
    if(!file.is_open()){
        LOG_E("File [%s] open fail!",file_name.c_str());
        return "";
    }
    
    file << file_stream;

    file.close();

    return true;
}

/**
 * @brief 判断文件是否存在
 * 
 * @param file_path 
 * @return true 
 * @return false 
 */
bool FileUtils::file_exist(std::string file_path)
{
    std::ifstream file(file_path.c_str(),std::ios::in | std::ios::binary);
    if(!file.is_open()){
        return false;
    }

    file.seekg(0, std::ios::end);
    std::streampos fileSize = file.tellg();
    file.seekg(0,std::ios::beg);
    if(fileSize == 0){
        std::cout<< "<" << file_path <<"> is empty!" <<std::endl;
        file.close();
        return false;
    }

    file.close();

    return true;
}

/**
 * @brief 删除文件
 * 
 * @param file_name 
 * @return true 
 * @return false 
 */
bool FileUtils:: delete_file(std::string file_name)
{
    if(0 == remove(file_name.c_str())){
        return true;
    }else{
        return false;
    }
}

#include "basic/os/os.h"
int FileUtils::open_file(std::string file_path)
{
    return OS::file_open(file_path);
}

int FileUtils::lock_file(int fd)
{
    return OS::file_lock(fd);
}

int FileUtils::unlock_file(int fd)
{
    return OS::file_unlock(fd);
}