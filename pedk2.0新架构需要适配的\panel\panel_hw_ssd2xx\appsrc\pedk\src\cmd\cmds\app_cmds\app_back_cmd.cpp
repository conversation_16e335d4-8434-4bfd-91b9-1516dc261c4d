#include "app_back_cmd.h"

AppBackCmd::AppBackCmd()
{
    m_app_back_cmd.type = MSG_APP_BACK;
}


AppBackCmd::~AppBackCmd()
{
}

uint32_t AppBackCmd::execute(AppCmdExec *app_cmd_exec)
{
    //执行一些退出流程，应该是调on.end函数
    app_cmd_exec->app_back();

    return 0;
}

std::string AppBackCmd::serialize()
{
    std::stringstream ss;
    msgpack::pack(ss, m_app_back_cmd);
    return ss.str();
}

uint32_t AppBackCmd::deserialize(std::string ss)
{
    //解码
    msgpack::object_handle oh = msgpack::unpack(ss.c_str(),ss.size());
    msgpack::object obj = oh.get();

    //将解码数据放到内部结构体中
    obj.convert(m_app_back_cmd);

    return 0;
}