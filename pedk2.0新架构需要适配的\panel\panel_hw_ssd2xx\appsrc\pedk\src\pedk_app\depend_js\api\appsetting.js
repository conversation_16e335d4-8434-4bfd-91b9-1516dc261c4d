/**
 * 本文件由PEDK API DSL 生成，请不要修改本文件
 */

import { isType } from "../common/utils.js";

import {
  setAppSettingValue_Impt,
  getAppSettingValue_Impt,
  getAllAppSettingValue_Impt,
  deleteAppSettingValue_Impt,
} from "../implement/index.js";

export function setAppSettingValue(name, json_value) {
  if (!isType(name, "String") || !isType(json_value, "Object")) {
    return "EINVALIDPARAM";
  }
  return setAppSettingValue_Impt(name, json_value);
}

export function getAppSettingValue(name) {
  if (!isType(name, "String")) {
    return "EINVALIDPARAM";
  }
  return getAppSettingValue_Impt(name);
}

export function getAllAppSettingValue() {
  return getAllAppSettingValue_Impt();
}

export function deleteAppSettingValue(name) {
  if (!isType(name, "String")) {
    return "EINVALIDPARAM";
  }
  return deleteAppSettingValue_Impt(name);
}
