#include "app_exit_cmd.h"

AppExitCmd::AppExitCmd(std::string app_name)
{
    m_app_exit_cmd.type = MSG_APP_EXIT;
    m_app_exit_cmd.app_name = app_name;
}

AppExitCmd::AppExitCmd()
{
    m_app_exit_cmd.type = MSG_APP_EXIT;
    m_app_exit_cmd.app_name = "";
}

AppExitCmd::~AppExitCmd()
{
}

uint32_t AppExitCmd::execute(AppCmdExec *app_cmd_exec)
{
    app_cmd_exec->app_exit(m_app_exit_cmd.app_name);
   
    return 0;
}

std::string AppExitCmd::serialize()
{
    std::stringstream ss;
    msgpack::pack(ss, m_app_exit_cmd);
    return ss.str();
}

uint32_t AppExitCmd::deserialize(std::string ss)
{
    //解码
    msgpack::object_handle oh = msgpack::unpack(ss.c_str(),ss.size());
    msgpack::object obj = oh.get();

    //将解码数据放到内部结构体中
    obj.convert(m_app_exit_cmd);

    return 0;
}