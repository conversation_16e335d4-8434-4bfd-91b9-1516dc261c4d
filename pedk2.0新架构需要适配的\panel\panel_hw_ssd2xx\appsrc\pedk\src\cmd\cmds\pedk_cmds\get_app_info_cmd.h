#ifndef _GET_APP_INFO_CMD_H_
#define _GET_APP_INFO_CMD_H_

#include "cmd/cmds/pedk_cmds/pedk_cmds.h"
#include "cmd/exec/pedk_cmd_exec.h"

typedef struct GetAppInfoCmdFormat {
    uint32_t type;        // 指令类型

    MSGPACK_DEFINE(type );
} GetAppInfoCmdFormat;

class GetAppInfoCmd: public PedkCmd{
public:
    GetAppInfoCmd();
    ~GetAppInfoCmd();
    uint32_t execute(PedkCmdExec *pedk_cmd_exec) override;
    std::string serialize() override;
    uint32_t deserialize(std::string ss) override;
protected:
private:
    GetAppInfoCmdFormat m_get_app_info_cmd;
};

#endif // _GET_APP_INFO_CMD_H_