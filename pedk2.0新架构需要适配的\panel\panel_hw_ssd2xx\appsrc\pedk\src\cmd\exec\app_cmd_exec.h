#ifndef _APP_CMD_EXEC_H_
#define _APP_CMD_EXEC_H_

//PEDK主进程与PEDK APP进程之间使用 APP CMD指令。
//此为PEDK系列指令执行动作的父类。
//同样的指令，在PEDK主进程端和PEDK APP进程之间执行的动作不同（即：一端有动作一端没动作）
#include "basic/config.h"

class AppCmdExec {
public:
    AppCmdExec(){};
    ~AppCmdExec(){};
    
    virtual void app_start_res(std::string app_name, bool res){};
    virtual void app_end(){};
    virtual void app_heart_beat(std::string app_name){};
    virtual void app_uplink_data(std::string target, std::string msg){};
    virtual void app_downlink_data(std::string msg){};
    virtual void app_front(){};
    virtual void app_back(){};
    virtual void app_exit(std::string app_name){};
    virtual void app_reset(std::string app_name){};
    virtual void app_error_info(std::string error_type, std::string error_info){};
    virtual void app_close_spec_app(std::string app_name){};
    virtual void app_reset_printer(){};
    virtual void app_get_session_id(const std::string& app_name){};
protected:
private:
    
};

#endif // _APP_CMD_EXEC_H_