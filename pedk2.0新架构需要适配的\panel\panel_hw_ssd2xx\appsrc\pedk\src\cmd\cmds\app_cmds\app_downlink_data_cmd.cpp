#include "app_downlink_data_cmd.h"

AppDownlinkDataCmd::AppDownlinkDataCmd()
{
    m_app_downlink_data_cmd.type = MSG_APP_DOWNLINK_DATA;
    m_app_downlink_data_cmd.msg = "";
}

AppDownlinkDataCmd::AppDownlinkDataCmd(std::string msg)
{
    m_app_downlink_data_cmd.type = MSG_APP_DOWNLINK_DATA;
    m_app_downlink_data_cmd.msg = msg;
}

AppDownlinkDataCmd::~AppDownlinkDataCmd()
{
}

uint32_t AppDownlinkDataCmd::execute(AppCmdExec *app_cmd_exec)
{
    app_cmd_exec->app_downlink_data(m_app_downlink_data_cmd.msg);

    return 0;
}

std::string AppDownlinkDataCmd::serialize()
{
    std::stringstream ss;
    msgpack::pack(ss, m_app_downlink_data_cmd);
    return ss.str();
}

uint32_t AppDownlinkDataCmd::deserialize(std::string ss)
{
    //解码
    msgpack::object_handle oh = msgpack::unpack(ss.c_str(),ss.size());
    msgpack::object obj = oh.get();

    //将解码数据放到内部结构体中
    obj.convert(m_app_downlink_data_cmd);

    LOG_I("Data deserialize");

    return 0;
}

std::string AppDownlinkDataCmd::get_msg()
{
    return m_app_downlink_data_cmd.msg;
}