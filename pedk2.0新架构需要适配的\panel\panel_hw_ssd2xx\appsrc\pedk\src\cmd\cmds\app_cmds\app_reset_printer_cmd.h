#ifndef _APP_RESET_PRINTER_H_
#define _APP_RESET_PRINTER_H_

#include "cmd/cmds/app_cmds/app_cmds.h"
#include "cmd/exec/app_cmd_exec.h"

//次指令只有特权APP可以调用
typedef struct AppResetPrinterCmdFormat {
    uint32_t type; // 指令类型

    MSGPACK_DEFINE(type);
} AppResetPrinterCmdFormat;

class AppResetPrinterCmd: public AppCmd{
public:
    AppResetPrinterCmd();
    ~AppResetPrinterCmd();
    uint32_t execute(AppCmdExec *app_cmd_exec) override;
    std::string serialize() override;
    uint32_t deserialize(std::string ss) override;
protected:
private:
    AppResetPrinterCmdFormat m_app_reset_printer_cmd;
};

#endif // _APP_RESET_PRINTER_H_
