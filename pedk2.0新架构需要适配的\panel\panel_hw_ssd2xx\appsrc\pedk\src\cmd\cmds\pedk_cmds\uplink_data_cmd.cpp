#include "uplink_data_cmd.h"

UplinkDataCmd::UplinkDataCmd()
{
    m_uplink_data_cmd.type = MSG_UPLINK_DATA;
    m_uplink_data_cmd.target_app_name = "";
    m_uplink_data_cmd.msg = "";
}

UplinkDataCmd::UplinkDataCmd(std::string target_app_name, std::string msg)
{
    m_uplink_data_cmd.type = MSG_UPLINK_DATA;
    m_uplink_data_cmd.target_app_name = target_app_name;
    m_uplink_data_cmd.msg = msg;
}

UplinkDataCmd::~UplinkDataCmd()
{
}

uint32_t UplinkDataCmd::execute(PedkCmdExec *pedk_cmd_exec)
{
    pedk_cmd_exec->uplink_data(m_uplink_data_cmd.target_app_name, m_uplink_data_cmd.msg);
    
    return 0;
}

std::string UplinkDataCmd::serialize()
{
    std::stringstream ss;
    msgpack::pack(ss, m_uplink_data_cmd);
    return ss.str();
}

uint32_t UplinkDataCmd::deserialize(std::string ss)
{
    //解码
    msgpack::object_handle oh = msgpack::unpack(ss.c_str(),ss.size());
    msgpack::object obj = oh.get();

    //将解码数据放到内部结构体中
    obj.convert(m_uplink_data_cmd);

    LOG_I("uplink data deserialize ok");

    return 0;
}