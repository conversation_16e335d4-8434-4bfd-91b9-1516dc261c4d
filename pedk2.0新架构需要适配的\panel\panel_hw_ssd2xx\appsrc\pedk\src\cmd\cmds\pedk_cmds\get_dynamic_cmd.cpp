#include "get_dynamic_cmd.h"

GetDynamicCmd::GetDynamicCmd()
{
    m_get_dynamic_cmd.type = MSG_GET_DYNAMIC_PROPERTY;
}

GetDynamicCmd::~GetDynamicCmd()
{
}

uint32_t GetDynamicCmd::execute(PedkCmdExec *pedk_cmd_exec)
{
    pedk_cmd_exec->get_dynamic_property();
   
    return 0;
}

std::string GetDynamicCmd::serialize()
{
    std::stringstream ss;
    msgpack::pack(ss, m_get_dynamic_cmd);

    return ss.str();
}

uint32_t GetDynamicCmd::deserialize(std::string ss)
{
    // 解码
    msgpack::object_handle oh = msgpack::unpack(ss.c_str(), ss.size());
    msgpack::object obj = oh.get();

    // 将解码数据放到内部结构体中
    obj.convert(m_get_dynamic_cmd);

    return 0;
}