#ifndef _DOWNLINK_DATA_CMD_H_
#define _DOWNLINK_DATA_CMD_H_

#include "cmd/cmds/pedk_cmds/pedk_cmds.h"
#include "cmd/exec/pedk_cmd_exec.h"

typedef struct DownlinkDataCmdFormat {
    uint32_t type;        // 指令类型
    std::string msg;      // 消息

    MSGPACK_DEFINE(type, msg);
} DownlinkDataCmdFormat;

class DownlinkDataCmd: public PedkCmd{
public:
    DownlinkDataCmd();
    DownlinkDataCmd(std::string msg);
    ~DownlinkDataCmd();
    uint32_t execute(PedkCmdExec *pedk_cmd_exec) override;
    std::string serialize() override;
    uint32_t deserialize(std::string ss) override;
protected:
private:
    DownlinkDataCmdFormat m_downlink_data_cmd;
};


#endif // _DOWNLINK_DATA_CMD_H_
