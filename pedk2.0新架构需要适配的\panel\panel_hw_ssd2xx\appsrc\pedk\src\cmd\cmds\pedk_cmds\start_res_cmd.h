#ifndef _START_RES_CMD_H_
#define _START_RES_CMD_H_

#include "cmd/cmds/pedk_cmds/pedk_cmds.h"
#include "cmd/exec/pedk_cmd_exec.h"

typedef struct StartResCmdFormat {
    uint32_t type;        // 指令类型
    std::string app_name; // APP
    bool result;          // 启动结果

    MSGPACK_DEFINE(type, app_name,result);
} StartResCmdFormat;

class StartResCmd: public PedkCmd{
public:
    StartResCmd();
    StartResCmd(std::string app_name, bool res);
    ~StartResCmd();
    uint32_t execute(PedkCmdExec *pedk_cmd_exec) override;
    std::string serialize() override;
    uint32_t deserialize(std::string ss) override;
protected:
private:
    StartResCmdFormat m_start_res_cmd;
};

#endif // _START_RES_CMD_H_