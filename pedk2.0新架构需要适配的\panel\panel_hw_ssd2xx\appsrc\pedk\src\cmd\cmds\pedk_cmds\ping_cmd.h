#ifndef _PING_CMD_H_
#define _PING_CMD_H_

#include "cmd/cmds/pedk_cmds/pedk_cmds.h"
#include "cmd/exec/pedk_cmd_exec.h"

typedef struct PingCmdFormat {
    uint32_t type; // 指令类型

    MSGPACK_DEFINE(type);
} PingCmdFormat;

class PingCmd : public PedkCmd{
public:
    PingCmd();
    ~PingCmd();
    uint32_t execute(PedkCmdExec *pedk_cmd_exec) override;
    std::string serialize() override;
    uint32_t deserialize(std::string ss) override;

protected:
private:
    
    PingCmdFormat m_ping_cmd;
};

#endif // _PING_CMD_H_