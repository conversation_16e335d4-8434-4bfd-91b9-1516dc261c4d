#include "pedk/pedk_transport/transport_pedk.h"
#include "basic/config.h"

TransportPedk* TransportPedk:: m_instance = nullptr;

TransportPedk* TransportPedk::get_instance()
{
    if(m_instance == nullptr){
        m_instance = new TransportPedk;
    }

    return m_instance;
}

TransportPedk::TransportPedk()
{
    m_ctx = TransportZMQ::create_ctx();
    m_zmq_router.start(m_ctx);
    m_zmq_pub.start(m_ctx);
}

TransportPedk::~TransportPedk()
{
}

uint32_t TransportPedk::send(std::string& message)
{
    //pedk进程默认选择走router通道
    m_zmq_pub.send_topic(CLIENT_NAME);
    return m_zmq_pub.send(message);
}

uint32_t TransportPedk::send_to_app(std::string topic, std::string& message)
{
    /*pedk选择走pub通道，将消息发送给app进程。有两种消息：
        1.广播消息,所有app都会收到消息。topic为："Broadcast"。
        2.指定消息，指定的app会收到消息。topic为：app名称。
    */
    m_zmq_pub.send_topic(topic);
    return m_zmq_pub.send(message);
}

int32_t TransportPedk::recv(std::string& message)
{
    return m_zmq_router.recv(message);
}

int TransportPedk::get_fd()
{
    return m_zmq_router.get_fd();
}

void* TransportPedk::get_handle()
{
    return m_zmq_router.get_socket();
}