#include "basic/config.h"
#include <iostream>
#include <stdarg.h>
#include <fstream>
//#include <filesystem>

std::string Log::log_level_to_str(enum LogLevel log_level)
{
    std::string ret;
    switch (log_level) {
    case PEDK_LOG_LEVEL_DEBUG:
        ret = "D";
        break;
    case PEDK_LOG_LEVEL_INFO:
        ret = "I";
        break;
    case PEDK_LOG_LEVEL_WARNING:
        ret = "W";
        break;
    case PEDK_LOG_LEVEL_ERROR:
        ret = "E";
        break;
    case PEDK_LOG_LEVEL_FATAL:
        ret = "F";
        break;
    }
    return ret;
}

#include "basic/utils/file_utils.h"
void Log::check_line_and_rename(LogFile &log_file, std::string new_name, std::string old_name)
{
    // 如果log行数大于最大值，则关闭本文件，然后备份一下，打开新的log。
    if (log_file.line_num > m_log_config.m_log_file_max_line) {
        //关闭当前文件
        log_file.log_ofstream.close(); 
        
        //如果新文件存在则删除
        if(FileUtils::file_exist(new_name)){
            FileUtils::delete_file(new_name);
        }

        //给当前log文件重命名未新文件名
        if(0 == std::rename(old_name.c_str(), new_name.c_str())){
            LOG_D("log rename SUCCESS!");
        }else{
            LOG_E("log rename FAIL!");
        }
        
        //再以旧文件名新开一个
        log_file.log_ofstream.open(old_name);
        log_file.line_num = 1;
    }
}

std::string Log::format_string(const char* format, ...)
{
    va_list args;
    va_start(args, format);
    
    char buffer[256];    
    int len = vsnprintf(buffer, sizeof(buffer), format, args);
std::cout<<buffer<<std::endl;
    if(0 <= len && len <  sizeof(buffer)){
        //如果format长度没超buffer，则直接使用
        std::string output(buffer);
        va_end(args);
        
        return output;
    }else{
        //如果format超长。则需要重新分配长度
        va_end(args);
        va_start(args,format);
        len = vsnprintf(nullptr,0,format,args) + 1;//重新计算所需长度，+1带上'\0'

        //分配足够大空间
        char *result = new char[len];
        vsnprintf(result, len, format,args);
        std::string output(result);
        delete[] result;
        va_end(args);

        return output;
    }
}

std::string Log::get_time()
{
    struct timeval tv;
    struct tm *tm_info;
    char time_str[30];

    gettimeofday(&tv,NULL);
    tm_info = localtime(&tv.tv_sec);
    size_t len = strftime(time_str, sizeof(time_str), LOG_TIME_FORMAT, tm_info);
    snprintf(time_str + len, sizeof(time_str) - len, ".%03ld",tv.tv_usec / 1000);

    std::string output(time_str);

    return output;
}