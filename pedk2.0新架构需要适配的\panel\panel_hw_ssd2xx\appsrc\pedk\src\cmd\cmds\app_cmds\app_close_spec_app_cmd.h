#ifndef _APP_CLOSE_SPEC_APP_CMD_H_
#define _APP_CLOSE_SPEC_APP_CMD_H_

#include "cmd/cmds/app_cmds/app_cmds.h"
#include "cmd/exec/app_cmd_exec.h"

//次指令只有特权APP可以调用
typedef struct AppCloseSpecAppCmdFormat {
    uint32_t type; // 指令类型
    std::string app_name; //要关闭的APP名

    MSGPACK_DEFINE(type, app_name);
} AppCloseSpecAppCmdFormat;

class AppCloseSpecAppCmd: public AppCmd{
public:
    AppCloseSpecAppCmd();
    AppCloseSpecAppCmd(std::string app_name);
    ~AppCloseSpecAppCmd();
    uint32_t execute(AppCmdExec *app_cmd_exec) override;
    std::string serialize() override;
    uint32_t deserialize(std::string ss) override;
protected:
private:
    AppCloseSpecAppCmdFormat m_app_close_spec_app_cmd;
};

#endif // _APP_CLOSE_SPEC_APP_CMD_H_
