#ifndef _TRANSPORT_PEDK_H_
#define _TRANSPORT_PEDK_H_

#include "transport/transport.h"
#include "transport/transport_zmq/transport_zmq.h"
#include "transport/transport_zmq/transport_zmq_router.h"
#include "transport/transport_zmq/transport_zmq_pub.h"

class TransportPedk: public Transport{
public:
   
    ~TransportPedk();

    uint32_t send(std::string& message) override;
    int32_t recv(std::string& message) override;
    int get_fd() override;
    void* get_handle() override;

    uint32_t send_to_app(std::string topic, std::string& message);

    static TransportPedk *get_instance();
protected:
private:
    TransportPedk();
    static TransportPedk* m_instance;
    TransportZMQRouter m_zmq_router;
    TransportZMQPub m_zmq_pub;
};

#endif // _TRANSPORT_PEDK_H_
