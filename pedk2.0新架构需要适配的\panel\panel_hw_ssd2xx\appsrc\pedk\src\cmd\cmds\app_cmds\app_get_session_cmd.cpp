#include "app_get_session_cmd.h"

AppGetSessionCmd::AppGetSessionCmd()
{
    m_get_session_cmd.type = MSG_GET_SESSION_ID;
}

AppGetSessionCmd::AppGetSessionCmd(const std::string& app_name)
{
    m_get_session_cmd.app_name = app_name;
    m_get_session_cmd.type = MSG_GET_SESSION_ID;
}



uint32_t AppGetSessionCmd::execute(AppCmdExec *app_cmd_exec)
{
    //pedk主进程为app分配唯一的session_Id
    app_cmd_exec->app_get_session_id(m_get_session_cmd.app_name);
    return 0;
}

std::string AppGetSessionCmd::serialize()
{
    std::stringstream ss;
    msgpack::pack(ss, m_get_session_cmd);
    return ss.str();
}

uint32_t AppGetSessionCmd::deserialize(std::string ss)
{
    //解码
    msgpack::object_handle oh = msgpack::unpack(ss.c_str(),ss.size());
    msgpack::object obj = oh.get();

    //将解码数据放到内部结构体中
    obj.convert(m_get_session_cmd);

    return 0;
}





