#ifndef _FILE_UTILS_H_
#define _FILE_UTILS_H_

#include<iostream>
#include<vector>

//文件操作工具类
class FileUtils {
public:
    static std::string load_file(std::string file_name);
    static bool load_binary(std::string file_name,std::vector<char> &out_binary);
    static bool store_file(std::string file_name, std::string file_stream);
    static bool file_exist(std::string file_path);
    static bool delete_file(std::string file_name);
    static int open_file(std::string file_path);
    static int lock_file(int file_handle);
    static int unlock_file(int file_handle);
protected:
private:
    
};

#endif // _FILE_UTILS_H_