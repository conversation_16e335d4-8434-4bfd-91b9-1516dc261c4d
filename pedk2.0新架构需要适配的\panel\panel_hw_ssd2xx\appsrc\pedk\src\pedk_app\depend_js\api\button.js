/**
 * 本文件由PEDK API DSL 生成，请不要修改本文件
 */

import { defProp } from "../common/utils.js";

import { StyleSheet } from "./style-sheet.js";

import { ImageData } from "./image-data.js";

export class Button {
  constructor() {
    defProp(this, "id", { types: ["String"], default: "" });
    defProp(this, "type", { types: ["String"], default: "button" });
    defProp(this, "x", { types: ["Int"], default: 0 });
    defProp(this, "y", { types: ["Int"], default: 0 });
    defProp(this, "w", { types: ["Int"], default: 0 });
    defProp(this, "h", { types: ["Int"], default: 0 });
    defProp(this, "style_sheet", { types: [StyleSheet] });
    defProp(this, "is_valid", { types: ["Boolean"] });
    defProp(this, "text", { types: ["String"], default: "" });
    defProp(this, "check_able", { types: ["Boolean"] });
    defProp(this, "is_checked", { types: ["Boolean"] });
    defProp(this, "imgs", { types: ["Array"], default: [] });
    defProp(this, "cb_pressed", { types: ["Function", "AsyncFunction"] });
    defProp(this, "cb_released", { types: ["Function", "AsyncFunction"] });
    defProp(this, "cb_long_pressed", { types: ["Function", "AsyncFunction"] });
    defProp(this, "cb_long_hold", { types: ["Function", "AsyncFunction"] });
    defProp(this, "cb_long_released", { types: ["Function", "AsyncFunction"] });
  }
}
