#include "app_reset_cmd.h"

AppResetCmd::AppResetCmd(std::string app_name)
{
    m_app_exit_cmd.type = MSG_APP_RESET;
    m_app_exit_cmd.app_name = app_name;
}

AppResetCmd::AppResetCmd()
{
    m_app_exit_cmd.type = MSG_APP_RESET;
    m_app_exit_cmd.app_name = "";
}

AppResetCmd::~AppResetCmd()
{
}

uint32_t AppResetCmd::execute(AppCmdExec *app_cmd_exec)
{
    app_cmd_exec->app_reset(m_app_exit_cmd.app_name);
   
    return 0;
}

std::string AppResetCmd::serialize()
{
    std::stringstream ss;
    msgpack::pack(ss, m_app_exit_cmd);
    return ss.str();
}

uint32_t AppResetCmd::deserialize(std::string ss)
{
    //解码
    msgpack::object_handle oh = msgpack::unpack(ss.c_str(),ss.size());
    msgpack::object obj = oh.get();

    //将解码数据放到内部结构体中
    obj.convert(m_app_exit_cmd);

    return 0;
}