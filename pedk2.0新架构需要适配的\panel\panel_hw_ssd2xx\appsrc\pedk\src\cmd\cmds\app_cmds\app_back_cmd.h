#ifndef _APP_BACK_CMD_H_
#define _APP_BACK_CMD_H_

#include "cmd/cmds/app_cmds/app_cmds.h"
#include "cmd/exec/app_cmd_exec.h"

typedef struct AppBackCmdFormat {
    uint32_t type; // 指令类型

    MSGPACK_DEFINE(type);
} AppBackCmdFormat;

class AppBackCmd: public AppCmd{
public:
    AppBackCmd();
    ~AppBackCmd();
    uint32_t execute(AppCmdExec *app_cmd_exec) override;
    std::string serialize() override;
    uint32_t deserialize(std::string ss) override;
protected:
private:
    AppBackCmdFormat m_app_back_cmd;
};

#endif // _APP_BACK_CMD_H_