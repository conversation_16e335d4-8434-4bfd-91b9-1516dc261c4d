#ifndef _APP_EXIT_CMD_H_
#define _APP_EXIT_CMD_H_

#include "cmd/cmds/app_cmds/app_cmds.h"
#include "cmd/exec/app_cmd_exec.h"

typedef struct AppExitCmdFormat {
    uint32_t type; // 指令类型
    std::string app_name;

    MSGPACK_DEFINE(type, app_name);
} AppExitCmdFormat;

class AppExitCmd: public AppCmd{
public:
    AppExitCmd(std::string app_name);
    AppExitCmd();
    ~AppExitCmd();
    uint32_t execute(AppCmdExec *app_cmd_exec) override;
    std::string serialize() override;
    uint32_t deserialize(std::string ss) override;
protected:
private:
    AppExitCmdFormat m_app_exit_cmd;
};

#endif // _APP_EXIT_CMD_H_

