#ifndef _END_RES_CMD_H_
#define _END_RES_CMD_H_

#include "cmd/cmds/pedk_cmds/pedk_cmds.h"
#include "cmd/exec/pedk_cmd_exec.h"

typedef struct EndResCmdFormat {
    uint32_t type;        // 指令类型
    std::string app_name; // APP
    bool result;       // 结束结果

    MSGPACK_DEFINE(type, app_name, result);
} EndResCmdFormat;

class EndResCmd: public PedkCmd{
public:
    EndResCmd();
    EndResCmd(std::string app_name, bool res);
    ~EndResCmd();
    uint32_t execute(PedkCmdExec *pedk_cmd_exec) override;
    std::string serialize() override;
    uint32_t deserialize(std::string ss) override;
protected:
private:
    EndResCmdFormat m_end_res_cmd;
};

#endif // _END_RES_CMD_H_