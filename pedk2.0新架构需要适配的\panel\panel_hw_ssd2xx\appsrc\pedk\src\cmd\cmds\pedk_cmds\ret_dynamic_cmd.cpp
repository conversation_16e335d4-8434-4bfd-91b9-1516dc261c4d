#include "ret_dynamic_cmd.h"

RetDynamicCmd::RetDynamicCmd(RetDynamicCmdFormat ret_dynamic_cmd)
{
    m_ret_dynamic_cmd = ret_dynamic_cmd;
    m_ret_dynamic_cmd.type = MSG_RET_DYNAMIC_PROPERTY;
    LOG_D("RetDynamicCmd by format");
}

RetDynamicCmd::RetDynamicCmd()
{
     m_ret_dynamic_cmd.type = MSG_RET_DYNAMIC_PROPERTY;
}

RetDynamicCmd::~RetDynamicCmd()
{
}

uint32_t RetDynamicCmd::execute(PedkCmdExec *pedk_cmd_exec)
{
    std::cout<<"RetDynamicCmd execute"<<std::endl;
    for(DynamicAttr it : m_ret_dynamic_cmd.apps_dynamic_arrt){
        pedk_cmd_exec->ret_dynamic_property(it.app_name, it.start_time);
    }

    return 0;
}

std::string RetDynamicCmd::serialize()
{
    std::stringstream ss;
    msgpack::pack(ss, m_ret_dynamic_cmd);

    return ss.str();
}

uint32_t RetDynamicCmd::deserialize(std::string ss)
{
    // 解码
    msgpack::object_handle oh = msgpack::unpack(ss.c_str(), ss.size());
    msgpack::object obj = oh.get();

    // 将解码数据放到内部结构体中
    obj.convert(m_ret_dynamic_cmd);

    return 0;
}