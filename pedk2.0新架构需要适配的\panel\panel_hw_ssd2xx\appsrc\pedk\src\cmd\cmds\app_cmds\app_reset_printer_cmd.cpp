#include "app_reset_printer_cmd.h"

AppResetPrinterCmd::AppResetPrinterCmd()
{
    m_app_reset_printer_cmd.type = MSG_APP_RESET_PRINTER;
}

AppResetPrinterCmd::~AppResetPrinterCmd()
{
}

uint32_t AppResetPrinterCmd::execute(AppCmdExec *app_cmd_exec)
{
    app_cmd_exec->app_reset_printer();

    return 0;
}

std::string AppResetPrinterCmd::serialize()
{
    std::stringstream ss;
    msgpack::pack(ss, m_app_reset_printer_cmd);
    return ss.str();
}

uint32_t AppResetPrinterCmd::deserialize(std::string ss)
{
    //解码
    msgpack::object_handle oh = msgpack::unpack(ss.c_str(),ss.size());
    msgpack::object obj = oh.get();

    //将解码数据放到内部结构体中
    obj.convert(m_app_reset_printer_cmd);

    LOG_I("Data deserialize");

    return 0;
}