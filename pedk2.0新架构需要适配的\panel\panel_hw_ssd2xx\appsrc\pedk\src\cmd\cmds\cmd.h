#ifndef _CMD_H_
#define _CMD_H_

#include <msgpack.hpp>
#include <vector>
#include "transport/transport.h"
#include <iostream>
#include "basic/config.h"

/* cmd指令设计说明
   （1）cmd指令分为PEDK CMD指令和APP CMD指令。
   （1.1）PEDK CMD:是客户端进程与PEDK主进程直接使用的指令
   （1.2）APP CMD:是PEDK主进程与PEDK_APP进程之间使用的指令
   （2）两种指令统一编址
   （3）指令使用msgpack进行序列化与反序列化
   （4）每种指令都会跨两个进程编译，通过各自进程的指令上下文，传递不同的执行者，执行不同的指令动作。
   （5）详细内容参考PEDK通信协议
*/
typedef enum {
    //PEDK CMD
    MSG_PING,
    MSG_PING_RES,
    MSG_START,
    MSG_START_RES,
    MSG_END,
    MSG_END_RES,
    MSG_UPLINK_DATA,
    MSG_DOWNLINK_DATA,
    MSG_GET_STATIC_PROPERTY,
    MSG_RET_STATIC_PROPERTY,
    MSG_GET_DYNAMIC_PROPERTY,
    MSG_RET_DYNAMIC_PROPERTY,
    MSG_RESET_PRINTER,
    MSG_GET_APP_INFO,
    MSG_RET_APP_INFO,
 
    MSG_RET_SESSION_ID,
    MSG_ERROR_INFO,

    //APP CMD
    MSG_APP_START_RES,
    MSG_APP_END,
    MSG_APP_HEART_BEAT,
    MSG_APP_UPLINK_DATA,
    MSG_APP_DOWNLINK_DATA,
    MSG_APP_FRONT,
    MSG_APP_BACK,
    MSG_APP_EXIT,
    MSG_APP_RESET,
    MSG_APP_SYS_ERR,
    MSG_APP_CLOSE_SPEC_APP,
    MSG_APP_RESET_PRINTER,
    MSG_GET_SESSION_ID
} MSG_CMD;

typedef struct MsgHeader{
    uint32_t type; // 指令类型
    MSGPACK_DEFINE(type);
}MsgHeader;

/* 全部指令的基类 */
class Cmd {
public:
    virtual std::string serialize() = 0;
    virtual uint32_t deserialize(std::string ss) = 0;
    MsgHeader m_msg_header;
private:
};

#endif // _CMD_H_