#include "downlink_data_cmd.h"
//#include "transport/transport_factory.h"
#include "basic/config.h"
//#include "modules/private/bridge.h"

DownlinkDataCmd::DownlinkDataCmd()
{
    m_downlink_data_cmd.type = MSG_DOWNLINK_DATA;
    m_downlink_data_cmd.msg = "";
}

DownlinkDataCmd::DownlinkDataCmd(std::string msg)
{
    m_downlink_data_cmd.type = MSG_DOWNLINK_DATA;
    m_downlink_data_cmd.msg = msg;
}

DownlinkDataCmd::~DownlinkDataCmd()
{
}

uint32_t DownlinkDataCmd::execute(PedkCmdExec *pedk_cmd_exec)
{
    pedk_cmd_exec->downlink_data(m_downlink_data_cmd.msg);

    return 0;
}

std::string DownlinkDataCmd::serialize()
{
    std::stringstream ss;
    msgpack::pack(ss, m_downlink_data_cmd);
    return ss.str();
}

uint32_t DownlinkDataCmd::deserialize(std::string ss)
{
    //解码
    msgpack::object_handle oh = msgpack::unpack(ss.c_str(),ss.size());
    msgpack::object obj = oh.get();

    //将解码数据放到内部结构体中
    obj.convert(m_downlink_data_cmd);

    LOG_I("Data deserialize");

    return 0;
}