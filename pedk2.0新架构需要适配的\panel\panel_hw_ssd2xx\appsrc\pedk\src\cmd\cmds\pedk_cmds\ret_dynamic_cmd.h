#ifndef _RET_DYNAMIC_CMD_H_
#define _RET_DYNAMIC_CMD_H_

#include "cmd/cmds/cmd.h"
#include <iostream>
#include <vector>
#include "cmd/cmds/pedk_cmds/pedk_cmds.h"
#include "cmd/exec/pedk_cmd_exec.h"

typedef struct DynamicAttr {
    std::string app_name; // 应用名
    std::string start_time;// 启动时间
    
    MSGPACK_DEFINE(app_name, start_time);
} DynamicAttr;

typedef struct RetDynamicCmdFormat {
    uint32_t type; // 指令类型
    std::vector<DynamicAttr> apps_dynamic_arrt;
    
    MSGPACK_DEFINE(type, apps_dynamic_arrt);
} RetDynamicCmdFormat;

class RetDynamicCmd : public PedkCmd {
public:
    RetDynamicCmd(RetDynamicCmdFormat ret_dynamic_cmd);
    RetDynamicCmd();
    ~RetDynamicCmd();
    uint32_t execute(PedkCmdExec *pedk_cmd_exec) override;
    std::string serialize() override;
    uint32_t deserialize(std::string ss) override;

protected:
private:
    RetDynamicCmdFormat m_ret_dynamic_cmd;
};

#endif // _RET_DYNAMIC_CMD_H_