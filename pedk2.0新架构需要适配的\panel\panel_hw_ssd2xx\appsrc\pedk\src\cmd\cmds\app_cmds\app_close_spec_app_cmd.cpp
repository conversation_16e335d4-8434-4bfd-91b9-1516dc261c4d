#include "app_close_spec_app_cmd.h"

AppCloseSpecAppCmd::AppCloseSpecAppCmd()
{
    m_app_close_spec_app_cmd.type = MSG_APP_CLOSE_SPEC_APP;
    m_app_close_spec_app_cmd.app_name = "";
}

AppCloseSpecAppCmd::AppCloseSpecAppCmd(std::string app_name)
{
    m_app_close_spec_app_cmd.type = MSG_APP_CLOSE_SPEC_APP;
    m_app_close_spec_app_cmd.app_name = app_name;
}

AppCloseSpecAppCmd::~AppCloseSpecAppCmd()
{
}

uint32_t AppCloseSpecAppCmd::execute(AppCmdExec *app_cmd_exec)
{
    app_cmd_exec->app_close_spec_app(m_app_close_spec_app_cmd.app_name);

    return 0;
}

std::string AppCloseSpecAppCmd::serialize()
{
    std::stringstream ss;
    msgpack::pack(ss, m_app_close_spec_app_cmd);
    return ss.str();
}

uint32_t AppCloseSpecAppCmd::deserialize(std::string ss)
{
    //解码
    msgpack::object_handle oh = msgpack::unpack(ss.c_str(),ss.size());
    msgpack::object obj = oh.get();

    //将解码数据放到内部结构体中
    obj.convert(m_app_close_spec_app_cmd);

    LOG_I("Data deserialize");

    return 0;
}