/**
 * 本文件由PEDK API DSL 生成，请不要修改本文件
 */

import { defProp } from "../common/utils.js";

import { StyleSheet } from "./style-sheet.js";

import { ImageData } from "./image-data.js";

export class AnimationImage {
  constructor() {
    defProp(this, "id", { types: ["String", "Int"] });
    defProp(this, "type", { types: ["String"], default: "animationimage" });
    defProp(this, "x", { types: ["Int"] });
    defProp(this, "y", { types: ["Int"] });
    defProp(this, "w", { types: ["Int"] });
    defProp(this, "h", { types: ["Int"] });
    defProp(this, "style_sheet", { types: [StyleSheet] });
    defProp(this, "imgs", { types: ["Array"] });
    defProp(this, "freq", { types: ["Int"] });
  }
}
